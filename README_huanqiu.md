# 环球网站自动注册脚本

## 功能特点

✅ **真实手机号生成** - 使用中国大陆真实手机号段  
✅ **智能密码生成** - 8-12位小写字母+数字混合  
✅ **设备指纹伪装** - 每个账号使用不同的iPhone设备指纹  
✅ **验证码自动识别** - 支持ddddocr自动识别，失败时手动输入  
✅ **完整加密实现** - 基于JavaScript逆向的AES加密签名
✅ **登录验证** - 注册成功后自动验证登录功能
✅ **OCR身份证识别** - 自动识别身份证正面的姓名和身份证号
✅ **自动实名认证** - 上传身份证图片并提交实名信息
✅ **代理IP支持** - 集成51代理，确保一个账号一个IP

## 安装依赖

```bash
pip install requests
pip install pycryptodome
pip install pillow
pip install ddddocr  # 验证码识别和身份证OCR识别
pip install opencv-python  # 可选，用于图像预处理
```

## 使用方法

### 1. 准备身份证图片
将身份证正面图片放入以下目录:
```
C:\Users\<USER>\Desktop\hook\zhuce\id_card_sorted\
└── 正面\  (身份证正面图片)
```
**注意**: 只需要正面图片即可，程序会自动识别姓名和身份证号

### 2. 配置代理 (可选)
如需使用代理IP，确保每个账号使用不同IP:
```bash
python proxy_config.py  # 配置51代理参数
```

### 3. 运行测试
```bash
python test_complete_flow.py  # 完整流程测试 (推荐)
python test_ocr.py           # OCR识别功能测试
python test_huanqiu.py       # 基本功能测试
```

### 4. 开始注册
```bash
python zhuccc1.py
```

### 5. 按提示操作
- 输入邀请码 (默认: 267524)
- 输入支付密码 (默认: 147258)
- 验证码会自动识别或手动输入
- 选择是否进行自动实名认证
- 如启用代理，每个账号将使用不同IP

## 核心技术

### 加密算法
- **AES加密**: 使用密钥 `v4NTEx37`
- **PBKDF2密钥派生**: 1000次迭代
- **随机IV和Salt**: 每次请求生成新的随机值

### 设备指纹
- **iPhone设备模拟**: 真实的iPhone型号和iOS版本
- **User-Agent**: 完全模拟抓包数据格式
- **设备参数**: 屏幕分辨率、设备ID等

### 请求格式
```
POST /api/login/register
Content-Type: application/x-www-form-urlencoded

sign={"ct":"加密内容","iv":"初始化向量","s":"盐值"}
&code_id=验证码ID
&code=验证码
&invitation=邀请码
&paypassword=支付密码
```

## 输出文件

注册成功的账号信息保存在:
```
C:\Users\<USER>\Desktop\hook\zhuce\xianbao_data\zhanghao.txt
```

格式: `手机号:密码:设备名称:用户ID:Token前缀:状态`

状态说明:
- `注册+登录+实名认证成功` - 完整流程成功
- `注册+登录成功,实名认证失败` - 注册登录成功但实名认证失败
- `注册+登录成功,跳过实名认证` - 用户选择跳过实名认证

## 注意事项

1. **验证码识别**: 建议安装ddddocr提高成功率
2. **请求频率**: 避免过于频繁的请求
3. **网络环境**: 确保网络连接稳定
4. **邀请码**: 使用有效的邀请码

## 故障排除

### 常见问题

**Q: 验证码识别失败**  
A: 安装ddddocr或手动输入验证码

**Q: 签名生成失败**  
A: 检查Crypto库是否正确安装

**Q: 注册失败**
A: 检查邀请码是否有效，网络是否正常

**Q: OCR识别失败**
A: 确保身份证图片清晰，或手动输入信息

**Q: 实名认证失败**
A: 检查身份证图片质量和网络连接

## 完整流程说明

### 📊 注册阶段:
1. 生成随机设备指纹（iPhone）
2. 生成真实手机号和安全密码
3. 获取并识别验证码
4. 生成AES加密签名
5. 发送注册请求
6. 验证登录功能

### 🆔 实名认证阶段:
7. 随机选择身份证图片
8. OCR识别身份证正面信息（姓名、身份证号）
9. 生成随机真实地址
10. 上传身份证正反面图片
11. 提交实名认证信息
12. 保存完整账号信息

### 🔍 OCR识别技术:
- **图像预处理**: 使用OpenCV增强图像对比度
- **OCR引擎**: ddddocr进行文字识别
- **智能解析**: 正则表达式提取姓名和身份证号
- **手动确认**: 识别失败时支持手动输入

### 🌐 代理IP功能:
- **51代理集成**: 支持51代理API自动获取IP
- **一账号一IP**: 确保每个注册账号使用不同的IP地址
- **协议支持**: 支持HTTP和SOCKS5代理协议
- **自动管理**: 自动获取、使用和释放代理IP
- **错误处理**: 代理失败时自动切换到直连模式

### 调试模式

脚本会输出详细的调试信息，包括:
- 设备指纹信息
- 加密签名过程
- 请求和响应内容
- 错误堆栈信息

## 更新日志

### v2.3 (2025-07-30)
- 🔄 **验证码重试** - OCR识别失败时自动获取新验证码图片
- 🎯 **4位数字验证** - 严格验证验证码为4位数字格式
- 📈 **增加重试次数** - 验证码重试次数增加到5次
- 🧪 **重试机制测试** - 新增验证码重试逻辑测试脚本
- 🖼️ **简化身份证处理** - 只需正面图片，自动显示图片供用户查看
- 📝 **增强手动输入** - OCR识别失败时提供手动输入选项
- 🔐 **修复密码长度** - 调整为6-11位符合服务器要求

### v2.2 (2025-07-30)
- 🌐 **代理IP支持** - 集成51代理API，确保一个账号一个IP
- 🔧 **代理配置工具** - 提供独立的代理配置脚本
- 🛡️ **IP隔离** - 每个注册账号使用不同的代理IP地址
- 📡 **协议支持** - 支持HTTP和SOCKS5代理协议
- 🔄 **自动管理** - 代理IP的自动获取、使用和释放
- 💾 **增强保存** - 账号信息包含代理IP，支持完整数据管理
- 📋 **账号管理** - 新增账号查看、导出和清理工具

### v2.1 (2025-07-30)
- 🔄 **智能重试机制** - 身份证已绑定时自动重试新身份证
- 📊 **状态确认** - 实名认证后自动确认最终状态
- 🧪 **完整测试套件** - 新增完整流程测试脚本
- 📋 **详细状态显示** - 显示认证姓名、身份证号等详细信息

### v2.0 (2025-07-30)
- 🆔 **新增实名认证功能** - 自动上传身份证并提交实名信息
- 🔍 **OCR身份证识别** - 自动识别身份证正面的姓名和身份证号
- 📷 **图像预处理** - 使用OpenCV增强识别准确率
- 🏠 **真实地址生成** - 生成中国真实地址信息
- 📋 **完整流程集成** - 注册→登录→实名认证一体化

### v1.0 (2025-07-30)
- 基于JavaScript逆向实现完整加密逻辑
- 支持真实手机号和智能密码生成
- 集成设备指纹伪装和验证码识别
- 添加登录验证功能

## 免责声明

本脚本仅用于技术学习和研究目的，请遵守相关法律法规和网站服务条款。
