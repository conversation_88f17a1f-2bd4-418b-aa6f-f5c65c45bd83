#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 模块1+2：注册登录 + 实名认证模块

import os
import sys
import json
import time
import random
import string
import requests
import hashlib
import base64
from datetime import datetime
from urllib.parse import urlencode
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from Crypto.Random import get_random_bytes

# 配置
BASE_URL = "https://ysfp.huanqiu.com"
ACCOUNTS_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\ysfp_accounts.txt"
ID_CARD_FRONT_DIR = r"C:\Users\<USER>\Desktop\hook\zhuce\id_card_sorted\正面"
AES_KEY = "v4NTEx37"

def evp_bytes_to_key(password: bytes, salt: bytes, key_len: int, iv_len: int) -> tuple:
    """模拟OpenSSL的EVP_BytesToKey方法"""
    derived_bytes = b''
    block = b''
    
    while len(derived_bytes) < key_len + iv_len:
        block = hashlib.md5(block + password + salt).digest()
        derived_bytes += block
    
    return derived_bytes[:key_len], derived_bytes[key_len:key_len + iv_len]

def generate_sign_parameter(username, password):
    """生成sign参数"""
    try:
        data_to_encrypt = {
            "username": username,
            "password": password
        }
        
        json_str = json.dumps(data_to_encrypt, separators=(',', ':'), ensure_ascii=False)
        
        iv = get_random_bytes(16)
        salt = get_random_bytes(8)
        
        key_bytes = AES_KEY.encode('utf-8')
        derived_key, _ = evp_bytes_to_key(key_bytes, salt, 32, 16)
        
        cipher = AES.new(derived_key, AES.MODE_CBC, iv)
        padded_data = pad(json_str.encode('utf-8'), AES.block_size)
        ciphertext = cipher.encrypt(padded_data)
        
        result = {
            "ct": base64.b64encode(ciphertext).decode('utf-8'),
            "iv": iv.hex(),
            "s": salt.hex()
        }
        
        return json.dumps(result, separators=(',', ':'))
    except Exception as e:
        print(f"❌ 生成sign参数失败: {e}")
        return None

def generate_random_password():
    """生成密码 - 基于解密发现的格式"""
    patterns = [
        lambda: generate_repeat_pattern(),    # 1111qqqq
        lambda: generate_sequence_pattern(),  # 1234abcd
        lambda: generate_random_pattern(),    # 5729xkwp
        lambda: generate_simple_combo(),      # 1234qwer
    ]
    
    weights = [0.4, 0.3, 0.2, 0.1]
    pattern_func = random.choices(patterns, weights=weights)[0]
    return pattern_func()

def generate_repeat_pattern():
    """重复模式 1111qqqq"""
    digit = random.choice(string.digits)
    letter = random.choice(string.ascii_lowercase)
    return digit * 4 + letter * 4

def generate_sequence_pattern():
    """序列模式 1234abcd"""
    start_digit = random.randint(0, 6)
    digit_part = ''.join(str(start_digit + i) for i in range(4))
    
    start_letter_idx = random.randint(0, 22)
    letter_part = ''.join(chr(ord('a') + start_letter_idx + i) for i in range(4))
    
    return digit_part + letter_part

def generate_random_pattern():
    """随机模式 5729xkwp"""
    digit_part = ''.join(random.choices(string.digits, k=4))
    letter_part = ''.join(random.choices(string.ascii_lowercase, k=4))
    return digit_part + letter_part

def generate_simple_combo():
    """简单组合 1234qwer"""
    simple_digits = ['1234', '5678', '9876', '1357', '2468']
    simple_letters = ['qwer', 'asdf', 'zxcv', 'abcd', 'efgh']
    return random.choice(simple_digits) + random.choice(simple_letters)

def generate_phone_number():
    """生成手机号"""
    prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                '150', '151', '152', '153', '155', '156', '157', '158', '159',
                '180', '181', '182', '183', '184', '185', '186', '187', '188', '189',
                '170', '171', '172', '173', '174', '175', '176', '177', '178', '179']
    
    prefix = random.choice(prefixes)
    suffix = ''.join(random.choices(string.digits, k=8))
    return prefix + suffix

def create_session():
    """创建会话"""
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': BASE_URL,
        'Referer': f'{BASE_URL}/',
    })
    return session

def get_captcha(session):
    """获取验证码"""
    try:
        response = session.get(f"{BASE_URL}/api/common/getCaptchaImg")
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1:
                return data['data']['code_id'], data['data']['img']
        return None, None
    except Exception as e:
        print(f"❌ 获取验证码失败: {e}")
        return None, None

def recognize_captcha(img_base64):
    """识别验证码"""
    try:
        import ddddocr
        ocr = ddddocr.DdddOcr(show_ad=False)
        img_data = base64.b64decode(img_base64.split(',')[1])
        result = ocr.classification(img_data)
        return result
    except Exception as e:
        print(f"❌ 验证码识别失败: {e}")
        return None

def register_account(session, phone, password, code_id, code, invitation="267524", paypassword="147258"):
    """注册账号"""
    try:
        sign = generate_sign_parameter(phone, password)
        if not sign:
            return False, "生成签名失败"
        
        data = {
            'sign': sign,
            'code_id': code_id,
            'code': code,
            'code_phone': '',
            'invitation': invitation,
            'paypassword': paypassword
        }
        
        response = session.post(f"{BASE_URL}/api/login/register", data=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 1:
                userinfo = result['data']['userinfo']
                return True, userinfo
            else:
                return False, result.get('msg', '注册失败')
        else:
            return False, f"请求失败: {response.status_code}"
            
    except Exception as e:
        return False, f"注册异常: {e}"

def login_account(session, phone, password):
    """登录账号"""
    try:
        sign = generate_sign_parameter(phone, password)
        if not sign:
            return False, "生成签名失败", None
        
        data = {'sign': sign}
        response = session.post(f"{BASE_URL}/api/login/login", data=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 1:
                userinfo = result['data']['userinfo']
                return True, "登录成功", userinfo
            else:
                return False, result.get('msg', '登录失败'), None
        else:
            return False, f"请求失败: {response.status_code}", None
            
    except Exception as e:
        return False, f"登录异常: {e}", None

def get_random_id_card_image():
    """获取随机身份证图片"""
    try:
        if not os.path.exists(ID_CARD_FRONT_DIR):
            print(f"❌ 身份证图片目录不存在: {ID_CARD_FRONT_DIR}")
            return None

        images = [f for f in os.listdir(ID_CARD_FRONT_DIR) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        if not images:
            print(f"❌ 身份证图片目录为空")
            return None

        selected_image = random.choice(images)
        image_path = os.path.join(ID_CARD_FRONT_DIR, selected_image)
        print(f"📷 选择的身份证图片: {selected_image}")
        return image_path

    except Exception as e:
        print(f"❌ 获取身份证图片失败: {e}")
        return None

def extract_id_info_from_filename(filename):
    """从文件名提取身份证信息"""
    try:
        # 文件名格式通常是: 姓名+地区+年份.jpg
        name_part = filename.replace('.jpg', '').replace('.jpeg', '').replace('.png', '')

        # 简单提取姓名（取前2-4个字符作为姓名）
        name = ""
        for i, char in enumerate(name_part):
            if char.isalpha() or '\u4e00' <= char <= '\u9fff':  # 中文字符
                name += char
                if len(name) >= 4:  # 最多4个字符的姓名
                    break
            elif name:  # 遇到非字母且已有姓名则停止
                break

        return name[:4] if name else None  # 限制姓名长度

    except Exception as e:
        print(f"❌ 从文件名提取信息失败: {e}")
        return None

def upload_id_card_image(session, image_path):
    """上传身份证图片"""
    try:
        with open(image_path, 'rb') as f:
            files = {'file': (os.path.basename(image_path), f, 'image/jpeg')}

            # 临时移除Content-Type让requests自动设置
            original_content_type = session.headers.get('Content-Type')
            if 'Content-Type' in session.headers:
                del session.headers['Content-Type']

            response = session.post(f"{BASE_URL}/api/common/upload", files=files)

            # 恢复Content-Type
            if original_content_type:
                session.headers['Content-Type'] = original_content_type

            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 1:
                    return result['data']['url']

        return None

    except Exception as e:
        print(f"❌ 上传图片失败: {e}")
        return None

def submit_real_name_auth(session, name, id_card, phone, image_url):
    """提交实名认证"""
    try:
        # 生成随机地址
        provinces = ["北京市", "上海市", "广东省", "江苏省", "浙江省", "山东省", "河南省", "四川省", "湖北省", "湖南省"]
        cities = ["市区", "新区", "开发区", "高新区"]
        streets = ["中山路", "人民路", "解放路", "建设路", "文化路", "学府路", "科技路", "商业街"]

        province = random.choice(provinces)
        city = random.choice(cities)
        street = random.choice(streets)
        number = random.randint(1, 999)
        address = f"{province}{city}{street}{number}号"

        data = {
            'name': name,
            'idcard': id_card,
            'address': address,
            'phone': phone,
            'img': image_url
        }

        response = session.post(f"{BASE_URL}/api/user/shiming", data=data)

        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 1:
                return True, "实名认证提交成功"
            else:
                return False, result.get('msg', '实名认证失败')
        else:
            return False, f"请求失败: {response.status_code}"

    except Exception as e:
        return False, f"实名认证异常: {e}"

def do_real_name_auth(session, phone):
    """执行实名认证流程"""
    try:
        # 1. 获取身份证图片
        image_path = get_random_id_card_image()
        if not image_path:
            return False, "", ""

        # 2. 从文件名提取姓名
        name = extract_id_info_from_filename(os.path.basename(image_path))
        if not name:
            name = input("请输入姓名: ").strip()

        # 3. 输入身份证号
        id_card = input(f"请输入身份证号 (姓名: {name}): ").strip()

        if not name or not id_card:
            print("❌ 姓名或身份证号不能为空")
            return False, "", ""

        # 4. 上传图片
        print(f"📤 上传身份证图片...")
        image_url = upload_id_card_image(session, image_path)
        if not image_url:
            print("❌ 图片上传失败")
            return False, "", ""

        print(f"✅ 图片上传成功: {image_url}")

        # 5. 提交实名认证
        print(f"📋 提交实名认证...")
        success, msg = submit_real_name_auth(session, name, id_card, phone, image_url)

        if success:
            print(f"✅ {msg}")
            return True, name, id_card
        else:
            print(f"❌ {msg}")
            return False, "", ""

    except Exception as e:
        print(f"❌ 实名认证流程异常: {e}")
        return False, "", ""

def save_account(phone, password, userinfo, real_name="", id_card=""):
    """保存账号信息到文件"""
    try:
        account_data = {
            'phone': phone,
            'password': password,
            'user_id': userinfo['user_id'],
            'token': userinfo['token'],
            'nickname': userinfo['nickname'],
            'real_name': real_name,
            'id_card': id_card,
            'create_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # 确保目录存在
        os.makedirs(os.path.dirname(ACCOUNTS_FILE), exist_ok=True)

        # 追加到文件
        with open(ACCOUNTS_FILE, 'a', encoding='utf-8') as f:
            f.write(json.dumps(account_data, ensure_ascii=False) + '\n')

        print(f"✅ 账号信息已保存到: {ACCOUNTS_FILE}")
        return True

    except Exception as e:
        print(f"❌ 保存账号失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 注册登录 + 实名认证模块")
    print("=" * 60)
    
    session = create_session()
    
    while True:
        print(f"\n📱 开始新账号注册...")
        
        # 生成账号信息
        phone = generate_phone_number()
        password = generate_random_password()
        
        print(f"📋 生成的账号信息:")
        print(f"   手机号: {phone}")
        print(f"   密码: {password}")
        
        # 获取验证码
        print(f"\n🔍 获取验证码...")
        code_id, img_base64 = get_captcha(session)
        if not code_id:
            print("❌ 获取验证码失败")
            continue
        
        # 识别验证码
        code = recognize_captcha(img_base64)
        if not code:
            print("❌ 验证码识别失败")
            continue
        
        print(f"✅ 验证码识别: {code}")
        
        # 注册账号
        print(f"\n🚀 注册账号...")
        success, result = register_account(session, phone, password, code_id, code)
        
        if success:
            print(f"✅ 注册成功!")
            print(f"   用户ID: {result['user_id']}")
            print(f"   Token: {result['token'][:20]}...")
            
            # 验证登录
            print(f"\n🔍 验证登录...")
            login_success, login_msg, login_info = login_account(session, phone, password)
            
            if login_success:
                print(f"✅ 登录验证成功!")
                
                # 询问是否进行实名认证
                do_auth = input(f"\n🆔 是否进行实名认证? (Y/N, 默认Y): ").strip().upper()
                
                if do_auth != 'N':
                    print(f"\n🆔 开始实名认证...")

                    # 设置token到session
                    session.headers.update({
                        'Authorization': f"Bearer {result['token']}"
                    })

                    # 进行实名认证
                    auth_success, real_name, id_card = do_real_name_auth(session, phone)

                    if auth_success:
                        print(f"✅ 实名认证成功: {real_name} ({id_card})")
                        save_account(phone, password, result, real_name, id_card)
                    else:
                        print(f"❌ 实名认证失败")
                        save_account(phone, password, result)
                else:
                    # 保存账号（不含实名信息）
                    save_account(phone, password, result)
                
            else:
                print(f"❌ 登录验证失败: {login_msg}")
                
        else:
            print(f"❌ 注册失败: {result}")
        
        # 询问是否继续
        continue_reg = input(f"\n是否继续注册下一个账号? (Y/N, 默认Y): ").strip().upper()
        if continue_reg == 'N':
            break
    
    print(f"\n🎉 注册模块结束!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        import traceback
        traceback.print_exc()
