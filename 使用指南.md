# 环球网站自动注册脚本 - 使用指南

## 🚀 快速开始

### 第一步：环境准备
```bash
# 1. 安装依赖
python install_requirements.py

# 2. 准备身份证图片
# 将身份证图片放入以下目录：
# C:\Users\<USER>\Desktop\hook\zhuce\id_card_sorted\正面\
# C:\Users\<USER>\Desktop\hook\zhuce\id_card_sorted\反面\
```

### 第二步：功能测试
```bash
# 完整功能测试（推荐）
python test_complete_flow.py

# 如果测试通过，继续下一步
```

### 第三步：开始注册
```bash
# 运行主脚本
python zhuccc1.py

# 按提示操作：
# - 输入邀请码（默认：267524）
# - 输入支付密码（默认：147258）
# - 选择是否进行实名认证（推荐选择Y）
```

## 📋 完整流程说明

### 🔐 注册阶段
1. **设备指纹生成** - 随机生成iPhone设备信息
2. **账号信息生成** - 真实手机号 + 安全密码
3. **验证码处理** - 自动识别或手动输入
4. **加密签名** - AES加密用户信息
5. **注册请求** - 提交注册数据
6. **登录验证** - 确认账号可用

### 🆔 实名认证阶段
7. **图片选择** - 随机选择身份证正反面
8. **OCR识别** - 自动识别姓名和身份证号
9. **地址生成** - 生成真实中国地址
10. **图片上传** - 上传身份证图片到服务器
11. **信息提交** - 提交实名认证信息
12. **状态确认** - 确认认证成功状态

## 🔍 功能特点

### ✅ 智能化
- **自动OCR识别** - 无需手动输入身份信息
- **智能重试** - 身份证已绑定时自动换新的
- **验证码重试** - OCR识别失败时自动获取新验证码
- **状态确认** - 自动确认每个步骤的成功状态

### ✅ 真实性
- **真实手机号** - 使用中国大陆真实号段
- **安全密码** - 8-12位字母数字混合
- **真实地址** - 中国真实省市区地址
- **设备伪装** - 每个账号使用不同iPhone设备

### ✅ 可靠性
- **完整加密** - 基于JavaScript逆向的AES加密
- **错误处理** - 详细的错误信息和重试机制
- **状态跟踪** - 实时显示每个步骤的进度

## 📊 输出结果

### 账号文件格式
```
手机号:密码:设备名称:用户ID:Token前缀:代理IP:状态
```

### 字段说明
- **手机号**: 注册使用的真实手机号
- **密码**: 随机生成的8-12位密码
- **设备名称**: iPhone设备型号 (如: iPhone 14 Pro)
- **用户ID**: 注册成功后的用户ID
- **Token前缀**: 登录Token的前20位
- **代理IP**: 使用的代理IP地址 (直连显示"直连")
- **状态**: 注册和认证状态

### 状态说明
- `注册+登录+实名认证成功` - 完整流程成功
- `注册+登录成功,实名认证失败` - 部分成功
- `注册+登录成功,跳过实名认证` - 用户选择跳过
- `登录验证失败` - 注册成功但登录验证失败

### 账号管理
```bash
python view_accounts.py  # 查看和管理保存的账号
```

功能包括:
- 📋 查看所有账号详情
- 📊 统计成功率和实名认证率
- 📤 导出账号信息为CSV格式
- 🗑️ 清理失败的账号记录

### 实名认证信息
脚本会显示：
- 认证姓名（OCR识别）
- 身份证号码（OCR识别）
- 认证地址（随机生成）
- 认证状态（服务器确认）

## ⚠️ 注意事项

### 身份证图片要求
- **格式**: JPG、PNG等常见格式
- **质量**: 清晰可见，文字不模糊
- **内容**: 确保姓名和身份证号码清晰
- **数量**: 建议准备多张不同的身份证图片

### 使用建议
- **测试先行**: 运行测试脚本确认环境正常
- **适度使用**: 避免过于频繁的注册请求
- **网络稳定**: 确保网络连接稳定
- **邀请码**: 使用有效的邀请码

### 故障排除
1. **OCR识别失败** - 检查图片质量，安装ddddocr
2. **网络错误** - 检查网络连接和防火墙
3. **身份证已绑定** - 脚本会自动重试新的身份证
4. **验证码错误** - 支持手动输入模式

## 🔧 高级配置

### 自定义配置
可以修改 `zhuccc1.py` 中的配置项：
- `BASE_URL` - 网站地址
- `OUTPUT_FILE` - 输出文件路径
- `ID_CARD_DIR` - 身份证图片目录

### 批量注册
脚本支持连续注册多个账号：
- 每次注册完成后询问是否继续
- 每个账号使用不同的设备指纹
- 自动避免重复使用身份证

## 📞 技术支持

### 常见问题
- 查看 `README_huanqiu.md` 中的故障排除部分
- 运行测试脚本诊断问题
- 检查依赖库是否正确安装

### 调试模式
脚本会输出详细的调试信息：
- 设备指纹详情
- 加密签名过程
- 请求响应内容
- OCR识别结果

## 🎯 成功率优化

### 提高成功率的建议
1. **准备充足的身份证图片** - 避免重复使用
2. **使用稳定的网络环境** - 减少请求失败
3. **安装完整的依赖库** - 特别是ddddocr和opencv
4. **定期更新脚本** - 适应网站变化

### 监控指标
- 注册成功率
- OCR识别准确率
- 实名认证通过率
- 网络请求成功率

---

**免责声明**: 本脚本仅用于技术学习和研究目的，请遵守相关法律法规和网站服务条款。
