#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 环球网站 - 自动注册脚本 (带验证码识别)

import requests
import json
import base64
import random
import string
import time
import re
from urllib.parse import urlencode
from io import BytesIO
from PIL import Image

# --- 配置项 ---
BASE_URL = "https://huanqiu.cgi913.com"
CAPTCHA_URL = f"{BASE_URL}/api/common/getCaptchaImg"
REGISTER_URL = f"{BASE_URL}/api/login/register"
OUTPUT_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\xianbao_data\zhanghao.txt" # 请确保路径存在

# 验证码识别相关
try:
    import ddddocr
    OCR_AVAILABLE = True
    print("✓ ddddocr 验证码识别库已加载")
except ImportError:
    OCR_AVAILABLE = False
    print("⚠ ddddocr 未安装，将使用手动输入验证码模式")

# --- 辅助函数 ---

def generate_random_phone():
    """生成随机手机号"""
    prefix = random.choice(['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                           '150', '151', '152', '153', '155', '156', '157', '158', '159',
                           '180', '181', '182', '183', '184', '185', '186', '187', '188', '189'])
    suffix = ''.join(random.choices(string.digits, k=8))
    return prefix + suffix

def generate_random_password(length=8):
    """生成随机密码，只包含数字"""
    return ''.join(random.choices(string.digits, k=length))

def generate_random_username():
    """生成随机用户名"""
    prefixes = ["user", "test", "demo", "temp", "guest"]
    suffix = ''.join(random.choices(string.digits, k=6))
    return random.choice(prefixes) + suffix

def get_captcha_image(session):
    """获取验证码图片"""
    try:
        print("正在获取验证码...")
        response = session.get(CAPTCHA_URL, headers={
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Connection': 'keep-alive',
            'Host': 'huanqiu.cgi913.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        })

        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1:
                code_id = data['data']['code_id']
                captcha_base64 = data['data']['captcha_src']

                # 解析base64图片
                if captcha_base64.startswith('data:image/png;base64,'):
                    captcha_base64 = captcha_base64.replace('data:image/png;base64,', '')

                captcha_image = base64.b64decode(captcha_base64)
                return code_id, captcha_image
            else:
                print(f"获取验证码失败: {data.get('msg', '未知错误')}")
                return None, None
        else:
            print(f"请求验证码失败，状态码: {response.status_code}")
            return None, None

    except Exception as e:
        print(f"获取验证码时发生错误: {e}")
        return None, None

def recognize_captcha(captcha_image):
    """识别验证码"""
    if OCR_AVAILABLE:
        try:
            ocr = ddddocr.DdddOcr()
            result = ocr.classification(captcha_image)
            print(f"自动识别验证码: {result}")
            return result
        except Exception as e:
            print(f"自动识别验证码失败: {e}")

    # 手动输入模式
    try:
        # 保存验证码图片供用户查看
        with open("captcha_temp.png", "wb") as f:
            f.write(captcha_image)
        print("验证码已保存为 captcha_temp.png，请查看后手动输入")

        # 尝试显示图片
        try:
            img = Image.open(BytesIO(captcha_image))
            img.show()
        except:
            pass

        captcha_code = input("请输入验证码: ").strip()
        return captcha_code
    except Exception as e:
        print(f"处理验证码时发生错误: {e}")
        return None

# --- 加密与请求函数 (保留您验证过的正确逻辑) ---

def encrypt_login_password(password: str) -> str:
    password_bytes = password.encode('utf-8')
    md5_hash_bytes = hashlib.md5(password_bytes).digest()
    cipher = AES.new(md5_hash_bytes, AES.MODE_CBC, PASSWORD_STATIC_IV)
    padded_data = pad(md5_hash_bytes, AES.block_size)
    aes_encrypted_bytes = cipher.encrypt(padded_data)
    return hashlib.md5(aes_encrypted_bytes).hexdigest()

def generate_mac_signature(key: bytes, content: str) -> str:
    h = hmac.new(key, content.encode('utf-8'), hashlib.md5)
    return base64.b64encode(h.digest()).decode('utf-8')

def encrypt_request_data(payload: dict) -> str:
    payload_str = json.dumps(payload, separators=(',', ':'), ensure_ascii=False)
    cipher = AES.new(GLOBAL_AES_KEY, AES.MODE_CBC, GLOBAL_AES_IV)
    padded_data = pad(payload_str.encode('utf-8'), AES.block_size)
    return base64.b64encode(cipher.encrypt(padded_data)).decode('utf-8')

def decrypt_response_data(encrypted_data_b64: str) -> dict:
    encrypted_bytes = base64.b64decode(encrypted_data_b64)
    cipher = AES.new(GLOBAL_AES_KEY, AES.MODE_CBC, GLOBAL_AES_IV)
    decrypted_padded_bytes = cipher.decrypt(encrypted_bytes)
    return json.loads(unpad(decrypted_padded_bytes, AES.block_size).decode('utf-8'))

# --- [新功能] 手机号验证预请求函数 ---
def verify_telephone_pre_request(session, phone, area_code, language, headers):
    print("\n--- [步骤1] 正在发送手机号验证预请求 ---")
    salt = str(int(time.time() * 1000))
    secret = base64.b64encode(random.getrandbits(128).to_bytes(16, 'big')).decode('utf-8')
    params = {'areaCode': area_code, 'telephone': phone, 'language': language, 'salt': salt, 'secret': secret}
    try:
        response = session.get(VERIFY_URL, params=params, headers=headers)
        response.raise_for_status()
        response_json = response.json()
        print("服务器响应:", json.dumps(response_json, ensure_ascii=False))
        if response_json.get("resultCode") == 1:
            print(">>> 手机号预验证成功，服务器已授权后续注册。")
            return True
        else:
            print(f">>> 手机号预验证失败: {response_json.get('resultMsg', '未知错误')}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"预请求网络错误: {e}")
        return False

# --- 主函数 (整合了所有新功能) ---
def auto_register():
    try:
        # [功能1] 交互式输入邀请码
        invite_code = input("请输入您的邀请码并按回车: ").strip()
        if not invite_code:
            print("错误：邀请码不能为空。程序已退出。")
            return

        # 固定的元数据
        area_code = "92"
        sex = "1"
        birthday = "1753510077"
        language = "zh"
        
        # [功能2] 动态生成设备指纹
        print("\n--- [动态] 正在生成本次注册信息 ---")
        fingerprint = generate_dynamic_fingerprint()
        device_model = fingerprint['model']
        device_os_version = fingerprint['os_version']
        device_serial = fingerprint['serial']

        # 生成其他随机信息
        phone = generate_random_phone()
        password = generate_random_password()
        nickname = f"{generate_random_chinese_name()}-黄朝凤团队"
        
        print(f"  手机号: {phone}\n  密码: {password}\n  昵称: {nickname}")
        print(f"  设备型号: {device_model}\n  安卓版本: {device_os_version}\n  设备序列号: {device_serial}")
        print(f"  邀请码: {invite_code} (由您输入)")

        session = requests.Session()
        headers = {
            "User-Agent": f"chat_im/2.1.8 (Linux; U; Android {device_os_version}; {device_model} Build/UP1A.231005.007)",
            "Connection": "Keep-Alive",
            "Accept-Encoding": "gzip"
        }
    
        # [功能3] 执行预请求
        if not verify_telephone_pre_request(session, phone, area_code, language, headers):
            print("\n!!! 流程终止，无法进行下一步注册。 !!!")
            return

        print("\n--- [步骤2] 准备正式注册请求 ---")
        salt = str(int(time.time() * 1000))
        encrypted_password = encrypt_login_password(password)
        mac_content_to_sign = f"212i919292901{area_code}{language}{phone}{sex}{salt}"
        mac_signature = generate_mac_signature(GLOBAL_AES_KEY, mac_content_to_sign)

        payload = {
            "birthday": birthday, "smsCode": "", "sex": sex, "telephone": phone,
            "cityId": "0", "provinceId": "0", "countryId": "0", "mac": mac_signature,
            "password": encrypted_password, "areaCode": area_code, "areaId": "0",
            "apiVersion": "76", "osVersion": device_os_version, "serial": device_serial,
            "inviteCode": invite_code, "idcard": "", "nickname": nickname,
            "isSmsRegister": "0", "name": "", "xmppVersion": "1",
            "model": device_model, "userType": "0"
        }
        
        encrypted_data = encrypt_request_data(payload)
        random_secret = base64.b64encode(random.getrandbits(128).to_bytes(16, 'big')).decode('utf-8')
        params = {"data": encrypted_data, "deviceId": "android", "language": language, "salt": salt, "secret": random_secret}

        print("--- 正在发送正式注册请求 ---")
        response = session.get(REGISTER_URL, params=params, headers=headers)
        response.raise_for_status()
        response_json = response.json()

        print("\n--- 服务器原始响应 ---")
        print(json.dumps(response_json, indent=2, ensure_ascii=False))

        if "data" in response_json and isinstance(response_json.get("data"), dict) and "data" in response_json["data"]:
            print("\n--- 尝试解密响应内容 ---")
            decrypted_resp = decrypt_response_data(response_json["data"]["data"])
            print(">>> 响应解密成功! <<<")
            print(json.dumps(decrypted_resp, indent=2, ensure_ascii=False))
            if response_json.get("resultCode") == 1:
                print("\n\n>>> 逻辑判断：\033[92m注册成功!\033[0m <<<") # 使用绿色高亮
                with open(OUTPUT_FILE, "a", encoding="utf-8") as f:
                    f.write(f"{phone}:{password}:{nickname}\n")
                print(f"\n账号信息已保存到: {OUTPUT_FILE}")
            else:
                print(f"\n\n>>> 逻辑判断：\033[91m注册失败 (ResultCode: {response_json.get('resultCode')}) - {response_json.get('resultMsg', '')}\033[0m <<<")
        else:
            error_msg = response_json.get("resultMsg", "未知错误")
            print(f"\n\n>>> 逻辑判断：\033[91m注册失败 ({error_msg})\033[0m <<<")

    except requests.exceptions.RequestException as e:
        print(f"\n网络请求错误: {e}")
    except KeyboardInterrupt:
        print("\n\n操作已由用户手动中断。程序退出。")
    except Exception as e:
        print(f"\n发生未知错误: {e}")

if __name__ == "__main__":
    try:
        while True:
            auto_register()
            user_input = input("\n是否继续注册下一个账号? (Y/N，直接回车=Y): ").strip().upper()
            if user_input == 'N':
                break
            print("-" * 50)
    except KeyboardInterrupt:
        print("\n\n程序已退出。")