#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 环球网站 - 自动注册脚本 (带验证码识别)

import requests
import json
import base64
import random
import string
import time
import re
import hashlib
import hmac
from urllib.parse import urlencode
from io import BytesIO
from PIL import Image
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes

# --- 配置项 ---
BASE_URL = "https://huanqiu.cgi913.com"
CAPTCHA_URL = f"{BASE_URL}/api/common/getCaptchaImg"
REGISTER_URL = f"{BASE_URL}/api/login/register"
OUTPUT_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\xianbao_data\zhanghao.txt" # 请确保路径存在

# 加密配置 (从JavaScript逆向得到)
AES_KEY = "v4NTEx37"  # 从JavaScript代码中找到的密钥
AES_KEY_BYTES = AES_KEY.encode('utf-8')

# 验证码识别相关
try:
    import ddddocr
    OCR_AVAILABLE = True
    print("✓ ddddocr 验证码识别库已加载")
except ImportError:
    OCR_AVAILABLE = False
    print("⚠ ddddocr 未安装，将使用手动输入验证码模式")

# 加密相关
import hashlib
import hmac
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes

# --- 核心加密函数 (基于JavaScript逆向) ---

def generate_sign_parameter(username, password):
    """
    生成sign参数 - 完全模拟JavaScript的加密逻辑
    对应JavaScript中的: yv.AES.encrypt(JSON.stringify({username, password}), "v4NTEx37", {format: Av}).toString()
    """
    try:
        # 1. 构造要加密的数据
        data_to_encrypt = {
            "username": username,
            "password": password
        }

        # 2. 转换为JSON字符串 (对应JavaScript的JSON.stringify)
        json_str = json.dumps(data_to_encrypt, separators=(',', ':'), ensure_ascii=False)
        print(f"要加密的JSON: {json_str}")

        # 3. 生成随机IV和Salt (模拟CryptoJS的行为)
        iv = get_random_bytes(16)  # 16字节的随机IV
        salt = get_random_bytes(8)  # 8字节的随机Salt

        # 4. 使用PBKDF2从密钥和salt生成实际的加密密钥 (模拟CryptoJS的密钥派生)
        from Crypto.Protocol.KDF import PBKDF2
        from Crypto.Hash import SHA1

        # CryptoJS默认使用PBKDF2进行密钥派生
        derived_key = PBKDF2(AES_KEY, salt, 32, count=1000, hmac_hash_module=SHA1)

        # 5. AES加密
        cipher = AES.new(derived_key, AES.MODE_CBC, iv)
        padded_data = pad(json_str.encode('utf-8'), AES.block_size)
        ciphertext = cipher.encrypt(padded_data)

        # 6. 构造结果对象 (对应JavaScript的Av.stringify函数)
        result = {
            "ct": base64.b64encode(ciphertext).decode('utf-8'),  # ciphertext转base64
            "iv": iv.hex(),  # IV转hex字符串
            "s": salt.hex()   # salt转hex字符串
        }

        # 7. 转换为JSON字符串 (对应JavaScript的JSON.stringify(t))
        sign_value = json.dumps(result, separators=(',', ':'))
        print(f"生成的sign参数: {sign_value}")

        return sign_value

    except Exception as e:
        print(f"生成sign参数时发生错误: {e}")
        return None

# --- 辅助函数 ---

def generate_random_phone():
    """生成随机手机号"""
    prefix = random.choice(['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                           '150', '151', '152', '153', '155', '156', '157', '158', '159',
                           '180', '181', '182', '183', '184', '185', '186', '187', '188', '189'])
    suffix = ''.join(random.choices(string.digits, k=8))
    return prefix + suffix

def generate_random_password():
    """生成8-12位小写英文和数字混合的随机密码"""
    length = random.randint(8, 12)  # 随机长度8-12位
    # 小写字母和数字
    chars = string.ascii_lowercase + string.digits

    # 确保至少包含一个字母和一个数字
    password = []
    password.append(random.choice(string.ascii_lowercase))  # 至少一个字母
    password.append(random.choice(string.digits))  # 至少一个数字

    # 填充剩余位数
    for _ in range(length - 2):
        password.append(random.choice(chars))

    # 打乱顺序
    random.shuffle(password)
    return ''.join(password)

def generate_real_phone_number():
    """生成真实格式的手机号"""
    # 中国大陆手机号段 (更真实的号段)
    prefixes = [
        # 中国移动
        '134', '135', '136', '137', '138', '139', '147', '150', '151',
        '152', '157', '158', '159', '178', '182', '183', '184', '187', '188',
        # 中国联通
        '130', '131', '132', '145', '155', '156', '166', '175', '176', '185', '186',
        # 中国电信
        '133', '149', '153', '173', '177', '180', '181', '189', '199'
    ]

    prefix = random.choice(prefixes)
    # 后8位数字
    suffix = ''.join(random.choices(string.digits, k=8))
    return prefix + suffix

def generate_device_fingerprint():
    """
    根据抓包数据格式生成真实的设备指纹
    基于实际iPhone设备的User-Agent格式
    """

    # 基于抓包数据的真实iPhone设备信息
    iphone_devices = [
        {
            "model": "iPhone13,2",  # iPhone 12
            "ios_version": "16_6",
            "device_name": "iPhone 12",
            "webkit_version": "605.1.15"
        },
        {
            "model": "iPhone14,3",  # iPhone 13 Pro
            "ios_version": "16_6",
            "device_name": "iPhone 13 Pro",
            "webkit_version": "605.1.15"
        },
        {
            "model": "iPhone15,2",  # iPhone 14
            "ios_version": "16_6",
            "device_name": "iPhone 14",
            "webkit_version": "605.1.15"
        },
        {
            "model": "iPhone12,1",  # iPhone 11
            "ios_version": "16_6",
            "device_name": "iPhone 11",
            "webkit_version": "605.1.15"
        },
        {
            "model": "iPhone11,8",  # iPhone XR
            "ios_version": "16_6",
            "device_name": "iPhone XR",
            "webkit_version": "605.1.15"
        }
    ]

    # 随机选择设备
    device = random.choice(iphone_devices)

    # 生成设备指纹信息
    fingerprint = {
        "model": device["model"],
        "ios_version": device["ios_version"],
        "device_name": device["device_name"],
        "webkit_version": device["webkit_version"],
        "safari_version": "604.1",

        # 生成唯一设备标识
        "device_id": generate_device_id(),
        "session_id": generate_session_id(),

        # 网络相关
        "connection_type": random.choice(["wifi", "cellular"]),
        "carrier": random.choice(["中国移动", "中国联通", "中国电信"]),

        # 屏幕信息 (iPhone真实分辨率)
        "screen_info": get_iphone_screen_info(device["model"]),

        # 时区和语言
        "timezone": "Asia/Shanghai",
        "language": "zh-CN",
        "locale": "zh_CN"
    }

    return fingerprint

def get_iphone_screen_info(model):
    """根据iPhone型号返回对应的屏幕信息"""
    screen_configs = {
        "iPhone13,2": {"width": 390, "height": 844, "scale": 3.0},  # iPhone 12
        "iPhone14,3": {"width": 393, "height": 852, "scale": 3.0},  # iPhone 13 Pro
        "iPhone15,2": {"width": 393, "height": 852, "scale": 3.0},  # iPhone 14
        "iPhone12,1": {"width": 414, "height": 896, "scale": 2.0},  # iPhone 11
        "iPhone11,8": {"width": 414, "height": 896, "scale": 2.0},  # iPhone XR
    }
    return screen_configs.get(model, {"width": 390, "height": 844, "scale": 3.0})

def generate_device_id():
    """生成设备ID (模拟iOS设备标识符格式)"""
    # iOS设备标识符格式: XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
    parts = [
        ''.join(random.choices(string.ascii_uppercase + string.digits, k=8)),
        ''.join(random.choices(string.ascii_uppercase + string.digits, k=4)),
        ''.join(random.choices(string.ascii_uppercase + string.digits, k=4)),
        ''.join(random.choices(string.ascii_uppercase + string.digits, k=4)),
        ''.join(random.choices(string.ascii_uppercase + string.digits, k=12))
    ]
    return '-'.join(parts)

def generate_session_id():
    """生成会话ID"""
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))

def generate_user_agent(fingerprint):
    """
    根据设备指纹生成User-Agent - 完全模拟抓包数据格式
    抓包数据: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
    """
    model = fingerprint["model"]
    ios_version = fingerprint["ios_version"]
    webkit_version = fingerprint["webkit_version"]
    safari_version = fingerprint["safari_version"]

    # 生成随机的Build号 (iOS格式: 15E148)
    build_number = generate_ios_build_number()

    # 构造完全符合抓包格式的User-Agent
    user_agent = (
        f"Mozilla/5.0 (iPhone; CPU iPhone OS {ios_version} like Mac OS X) "
        f"AppleWebKit/{webkit_version} (KHTML, like Gecko) "
        f"Version/{ios_version.replace('_', '.')} Mobile/{build_number} Safari/{safari_version}"
    )

    return user_agent

def generate_ios_build_number():
    """生成iOS Build号格式 (如: 15E148)"""
    # iOS Build号格式: [主版本号][字母][数字]
    major_version = random.choice(['15', '16', '17'])
    letter = random.choice(['A', 'B', 'C', 'D', 'E', 'F', 'G'])
    number = random.randint(100, 999)
    return f"{major_version}{letter}{number}"

def generate_request_headers(fingerprint, extra_headers=None):
    """
    生成完整的请求头 - 基于抓包数据
    """
    user_agent = generate_user_agent(fingerprint)

    # 基础请求头 (完全模拟抓包数据)
    headers = {
        'User-Agent': user_agent,
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Connection': 'keep-alive',
        'Host': 'huanqiu.cgi913.com',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Token': '',  # 从抓包数据中看到的Token字段
    }

    # 添加额外的请求头
    if extra_headers:
        headers.update(extra_headers)

    return headers

def generate_device_fingerprint_for_request():
    """
    为请求生成设备指纹参数
    可能需要在请求中包含的设备相关参数
    """
    fingerprint = generate_device_fingerprint()

    # 可能在请求中需要的设备参数
    device_params = {
        'device_id': fingerprint['device_id'],
        'device_type': 'ios',
        'device_model': fingerprint['model'],
        'os_version': fingerprint['ios_version'].replace('_', '.'),
        'app_version': '1.0.0',  # 假设的应用版本
        'screen_width': fingerprint['screen_info']['width'],
        'screen_height': fingerprint['screen_info']['height'],
        'timezone': fingerprint['timezone'],
        'language': fingerprint['language'],
    }

    return fingerprint, device_params

def get_captcha_image(session):
    """获取验证码图片"""
    try:
        print("正在获取验证码...")
        response = session.get(CAPTCHA_URL, headers={
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Connection': 'keep-alive',
            'Host': 'huanqiu.cgi913.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        })

        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1:
                code_id = data['data']['code_id']
                captcha_base64 = data['data']['captcha_src']

                # 解析base64图片
                if captcha_base64.startswith('data:image/png;base64,'):
                    captcha_base64 = captcha_base64.replace('data:image/png;base64,', '')

                captcha_image = base64.b64decode(captcha_base64)
                return code_id, captcha_image
            else:
                print(f"获取验证码失败: {data.get('msg', '未知错误')}")
                return None, None
        else:
            print(f"请求验证码失败，状态码: {response.status_code}")
            return None, None

    except Exception as e:
        print(f"获取验证码时发生错误: {e}")
        return None, None

def recognize_captcha(captcha_image, max_retries=3):
    """
    智能验证码识别 - 优先使用ddddocr自动识别，失败时提供手动输入
    """
    if OCR_AVAILABLE:
        print("🤖 使用ddddocr自动识别验证码...")

        for attempt in range(max_retries):
            try:
                # 创建OCR实例
                ocr = ddddocr.DdddOcr(show_ad=False)

                # 识别验证码
                result = ocr.classification(captcha_image)

                # 验证结果格式 (通常验证码是4位数字)
                if result and len(result) >= 3 and result.isdigit():
                    print(f"✅ 自动识别成功: {result}")
                    return result
                else:
                    print(f"⚠️ 识别结果可疑: {result} (尝试 {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        continue

            except Exception as e:
                print(f"❌ 自动识别失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    continue

        print("🔄 自动识别多次失败，切换到手动输入模式...")
    else:
        print("📝 ddddocr未安装，使用手动输入模式...")

    # 手动输入模式
    try:
        # 保存验证码图片供用户查看
        captcha_filename = f"captcha_{int(time.time())}.png"
        with open(captcha_filename, "wb") as f:
            f.write(captcha_image)
        print(f"💾 验证码已保存为: {captcha_filename}")

        # 尝试自动显示图片
        try:
            img = Image.open(BytesIO(captcha_image))
            img.show()
            print("🖼️ 验证码图片已自动打开")
        except Exception as e:
            print(f"⚠️ 无法自动显示图片: {e}")
            print(f"📂 请手动打开文件: {captcha_filename}")

        # 获取用户输入
        while True:
            captcha_code = input("\n🔤 请输入验证码 (输入 'retry' 重新获取): ").strip()

            if captcha_code.lower() == 'retry':
                return 'retry'
            elif captcha_code and len(captcha_code) >= 3:
                print(f"✅ 手动输入验证码: {captcha_code}")
                return captcha_code
            else:
                print("❌ 验证码格式不正确，请重新输入")

    except Exception as e:
        print(f"💥 处理验证码时发生错误: {e}")
        return None

# --- 登录函数 ---

def auto_login(phone_number, password, session=None):
    """自动登录验证账号"""
    try:
        print(f"\n🔑 正在验证登录: {phone_number}")

        if session is None:
            session = requests.Session()
            # 生成设备指纹和请求头
            fingerprint, _ = generate_device_fingerprint_for_request()
            headers = generate_request_headers(fingerprint)
            session.headers.update(headers)

        # 生成登录的sign参数 (只包含手机号和密码)
        login_sign = generate_sign_parameter(phone_number, password)

        if not login_sign:
            print("❌ 生成登录签名失败")
            return False, None

        # 构造登录请求数据
        login_data = {
            'sign': login_sign
        }

        # 发送登录请求
        login_url = f"{BASE_URL}/api/login/login"
        response = session.post(login_url, data=login_data)

        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('code') == 1:
                    userinfo = result.get('data', {}).get('userinfo', {})
                    token = userinfo.get('token', '')
                    user_id = userinfo.get('user_id', '')

                    print(f"✅ 登录成功!")
                    print(f"   用户ID: {user_id}")
                    print(f"   Token: {token[:20]}...")
                    print(f"   过期时间: {userinfo.get('expires_in', 0)}秒")

                    return True, {
                        'token': token,
                        'user_id': user_id,
                        'userinfo': userinfo
                    }
                else:
                    print(f"❌ 登录失败: {result.get('msg', '未知错误')}")
                    return False, None
            except json.JSONDecodeError:
                print(f"❌ 登录响应解析失败: {response.text}")
                return False, None
        else:
            print(f"❌ 登录请求失败，状态码: {response.status_code}")
            return False, None

    except Exception as e:
        print(f"💥 登录过程中发生错误: {e}")
        return False, None

# --- 主要注册函数 ---

def auto_register():
    """自动注册账号 - 集成设备指纹伪装"""
    try:
        print("=" * 60)
        print("🚀 环球网站自动注册脚本启动")
        print("=" * 60)

        # 1. 生成设备指纹
        print("📱 正在生成设备指纹...")
        fingerprint, device_params = generate_device_fingerprint_for_request()

        print(f"🔧 设备信息:")
        print(f"   设备: {fingerprint['device_name']}")
        print(f"   型号: {fingerprint['model']}")
        print(f"   iOS版本: {fingerprint['ios_version'].replace('_', '.')}")
        print(f"   屏幕分辨率: {fingerprint['screen_info']['width']}x{fingerprint['screen_info']['height']}")
        print(f"   设备ID: {fingerprint['device_id'][:20]}...")
        print(f"   语言: {fingerprint['language']}")
        print(f"   时区: {fingerprint['timezone']}")

        # 2. 生成随机账号信息 (使用真实手机号)
        phone_number = generate_real_phone_number()
        password = generate_random_password()

        print(f"\n� 生成的账号信息:")
        print(f"   手机号: {phone_number}")
        print(f"   密码: {password} (长度: {len(password)}位)")

        # 3. 创建会话并设置请求头 (完全模拟抓包数据)
        session = requests.Session()

        # 生成完整的请求头
        base_headers = generate_request_headers(fingerprint)
        session.headers.update(base_headers)

        print(f"\n🌐 User-Agent: {base_headers['User-Agent'][:80]}...")

        # 4. 获取验证码 (支持重试)
        max_captcha_retries = 3
        for captcha_attempt in range(max_captcha_retries):
            print(f"\n🔍 正在获取验证码... (尝试 {captcha_attempt + 1}/{max_captcha_retries})")
            code_id, captcha_image = get_captcha_image(session)

            if not code_id or not captcha_image:
                print("❌ 获取验证码失败")
                if captcha_attempt < max_captcha_retries - 1:
                    print("🔄 等待3秒后重试...")
                    time.sleep(3)
                    continue
                else:
                    return False

            # 5. 识别验证码
            print("🤖 正在识别验证码...")
            captcha_code = recognize_captcha(captcha_image)

            if captcha_code == 'retry':
                print("🔄 用户选择重新获取验证码...")
                continue
            elif not captcha_code:
                print("❌ 验证码识别失败")
                if captcha_attempt < max_captcha_retries - 1:
                    continue
                else:
                    return False

            print(f"✅ 验证码识别成功: {captcha_code}")
            break
        else:
            print("❌ 验证码获取/识别失败次数过多")
            return False

        # 6. 获取邀请码和支付密码
        invitation = input("\n🎫 请输入邀请码 (默认: 267524): ").strip()
        if not invitation:
            invitation = "267524"

        paypassword = input("🔐 请输入支付密码 (默认: 147258): ").strip()
        if not paypassword:
            paypassword = "147258"

        # 7. 生成sign参数
        print("\n🔐 正在生成加密签名...")
        sign_value = generate_sign_parameter(phone_number, password)

        if not sign_value:
            print("❌ 生成签名失败")
            return False

        # 8. 构造注册请求数据
        register_data = {
            'sign': sign_value,
            'code_id': code_id,
            'code': captcha_code,
            'invitation': invitation,
            'paypassword': paypassword
        }

        print(f"\n📤 准备发送注册请求...")
        print(f"   设备: {fingerprint['device_name']}")
        print(f"   code_id: {code_id}")
        print(f"   验证码: {captcha_code}")
        print(f"   邀请码: {invitation}")
        print(f"   支付密码: {paypassword}")

        # 9. 发送注册请求
        register_headers = base_headers.copy()
        register_headers.update({
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://huanqiu.cgi913.com',
            'Referer': 'https://huanqiu.cgi913.com/',
        })

        print("\n🚀 发送注册请求...")
        response = session.post(REGISTER_URL, data=register_data, headers=register_headers)

        print(f"\n📥 服务器响应:")
        print(f"   状态码: {response.status_code}")

        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

                if result.get('code') == 1:
                    print("\n🎉 注册成功!")

                    # 获取注册返回的用户信息
                    userinfo = result.get('data', {}).get('userinfo', {})
                    user_id = userinfo.get('user_id', '')
                    token = userinfo.get('token', '')

                    print(f"📋 注册信息:")
                    print(f"   用户ID: {user_id}")
                    print(f"   Token: {token[:20]}...")
                    print(f"   昵称: {userinfo.get('nickname', '')}")

                    # 验证登录功能
                    print("\n🔍 验证登录功能...")
                    login_success, login_info = auto_login(phone_number, password, session)

                    if login_success:
                        print("✅ 登录验证通过!")

                        # 保存完整的账号信息
                        account_info = (
                            f"{phone_number}:{password}:{fingerprint['device_name']}:"
                            f"{user_id}:{token[:20]}...\n"
                        )
                        with open(OUTPUT_FILE, "a", encoding="utf-8") as f:
                            f.write(account_info)

                        print(f"✅ 账号信息已保存到: {OUTPUT_FILE}")
                        print(f"📱 设备信息: {fingerprint['device_name']} ({fingerprint['model']})")
                        return True
                    else:
                        print("⚠️ 注册成功但登录验证失败")
                        # 仍然保存账号信息
                        account_info = f"{phone_number}:{password}:{fingerprint['device_name']}:登录验证失败\n"
                        with open(OUTPUT_FILE, "a", encoding="utf-8") as f:
                            f.write(account_info)
                        return True
                else:
                    print(f"\n❌ 注册失败: {result.get('msg', '未知错误')}")
                    return False

            except json.JSONDecodeError:
                print(f"   响应内容 (非JSON): {response.text}")
                return False
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False

    except Exception as e:
        print(f"\n💥 注册过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

# --- 主程序入口 ---

if __name__ == "__main__":
    try:
        print("🌟 环球网站自动注册脚本")
        print("🔧 基于JavaScript逆向分析实现")
        print("=" * 50)

        while True:
            success = auto_register()

            if success:
                print("\n✅ 本次注册完成!")
            else:
                print("\n❌ 本次注册失败!")

            user_input = input("\n是否继续注册下一个账号? (Y/N，直接回车=Y): ").strip().upper()
            if user_input == 'N':
                break
            print("\n" + "=" * 50)

    except KeyboardInterrupt:
        print("\n\n👋 程序已退出。")
    except Exception as e:
        print(f"\n💥 程序运行时发生错误: {e}")
        import traceback
        traceback.print_exc()