#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 环球网站 - 自动注册脚本 (带验证码识别)

import requests
import json
import base64
import random
import string
import time
import re
import hashlib
import hmac
from urllib.parse import urlencode
from io import BytesIO
from PIL import Image
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes

# --- 配置项 ---
BASE_URL = "https://huanqiu.cgi913.com"
CAPTCHA_URL = f"{BASE_URL}/api/common/getCaptchaImg"
REGISTER_URL = f"{BASE_URL}/api/login/register"
OUTPUT_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\xianbao_data\zhanghao.txt" # 请确保路径存在

# 加密配置 (从JavaScript逆向得到)
AES_KEY = "v4NTEx37"  # 从JavaScript代码中找到的密钥
AES_KEY_BYTES = AES_KEY.encode('utf-8')

# 验证码识别相关
try:
    import ddddocr
    OCR_AVAILABLE = True
    print("✓ ddddocr 验证码识别库已加载")
except ImportError:
    OCR_AVAILABLE = False
    print("⚠ ddddocr 未安装，将使用手动输入验证码模式")

# 加密相关
import hashlib
import hmac
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes

# --- 核心加密函数 (基于JavaScript逆向) ---

def generate_sign_parameter(username, password):
    """
    生成sign参数 - 完全模拟JavaScript的加密逻辑
    对应JavaScript中的: yv.AES.encrypt(JSON.stringify({username, password}), "v4NTEx37", {format: Av}).toString()
    """
    try:
        # 1. 构造要加密的数据
        data_to_encrypt = {
            "username": username,
            "password": password
        }

        # 2. 转换为JSON字符串 (对应JavaScript的JSON.stringify)
        json_str = json.dumps(data_to_encrypt, separators=(',', ':'), ensure_ascii=False)
        print(f"要加密的JSON: {json_str}")

        # 3. 生成随机IV和Salt (模拟CryptoJS的行为)
        iv = get_random_bytes(16)  # 16字节的随机IV
        salt = get_random_bytes(8)  # 8字节的随机Salt

        # 4. 使用PBKDF2从密钥和salt生成实际的加密密钥 (模拟CryptoJS的密钥派生)
        from Crypto.Protocol.KDF import PBKDF2
        from Crypto.Hash import SHA1

        # CryptoJS默认使用PBKDF2进行密钥派生
        derived_key = PBKDF2(AES_KEY, salt, 32, count=1000, hmac_hash_module=SHA1)

        # 5. AES加密
        cipher = AES.new(derived_key, AES.MODE_CBC, iv)
        padded_data = pad(json_str.encode('utf-8'), AES.block_size)
        ciphertext = cipher.encrypt(padded_data)

        # 6. 构造结果对象 (对应JavaScript的Av.stringify函数)
        result = {
            "ct": base64.b64encode(ciphertext).decode('utf-8'),  # ciphertext转base64
            "iv": iv.hex(),  # IV转hex字符串
            "s": salt.hex()   # salt转hex字符串
        }

        # 7. 转换为JSON字符串 (对应JavaScript的JSON.stringify(t))
        sign_value = json.dumps(result, separators=(',', ':'))
        print(f"生成的sign参数: {sign_value}")

        return sign_value

    except Exception as e:
        print(f"生成sign参数时发生错误: {e}")
        return None

# --- 辅助函数 ---

def generate_random_phone():
    """生成随机手机号"""
    prefix = random.choice(['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                           '150', '151', '152', '153', '155', '156', '157', '158', '159',
                           '180', '181', '182', '183', '184', '185', '186', '187', '188', '189'])
    suffix = ''.join(random.choices(string.digits, k=8))
    return prefix + suffix

def generate_random_password(length=8):
    """生成随机密码，只包含数字"""
    return ''.join(random.choices(string.digits, k=length))

def generate_random_username():
    """生成随机用户名"""
    prefixes = ["user", "test", "demo", "temp", "guest"]
    suffix = ''.join(random.choices(string.digits, k=6))
    return random.choice(prefixes) + suffix

def generate_device_fingerprint():
    """生成随机设备指纹，确保每个账号使用不同的设备"""

    # 真实的手机品牌和型号
    devices = [
        # 华为系列
        {"brand": "HUAWEI", "model": "ELE-AL00", "name": "华为P30"},
        {"brand": "HUAWEI", "model": "VOG-AL00", "name": "华为P30 Pro"},
        {"brand": "HUAWEI", "model": "MAR-AL00", "name": "华为P30 Lite"},
        {"brand": "HUAWEI", "model": "LYA-AL00", "name": "华为Mate 20"},
        {"brand": "HUAWEI", "model": "HMA-AL00", "name": "华为Mate 20 Pro"},

        # 小米系列
        {"brand": "Xiaomi", "model": "MI 9", "name": "小米9"},
        {"brand": "Xiaomi", "model": "MI 10", "name": "小米10"},
        {"brand": "Xiaomi", "model": "MI 11", "name": "小米11"},
        {"brand": "Redmi", "model": "Redmi K30", "name": "红米K30"},
        {"brand": "Redmi", "model": "Redmi Note 9", "name": "红米Note 9"},

        # OPPO系列
        {"brand": "OPPO", "model": "PCLM10", "name": "OPPO Find X2"},
        {"brand": "OPPO", "model": "CPH2025", "name": "OPPO Reno4"},
        {"brand": "OPPO", "model": "CPH2127", "name": "OPPO A92s"},

        # vivo系列
        {"brand": "vivo", "model": "V1955A", "name": "vivo X50"},
        {"brand": "vivo", "model": "V1981A", "name": "vivo S6"},
        {"brand": "vivo", "model": "V1963A", "name": "vivo Y50"},

        # 三星系列
        {"brand": "samsung", "model": "SM-G9730", "name": "Galaxy S10"},
        {"brand": "samsung", "model": "SM-G9750", "name": "Galaxy S10+"},
        {"brand": "samsung", "model": "SM-N9700", "name": "Galaxy Note10"},
    ]

    # 随机选择设备
    device = random.choice(devices)

    # Android版本 (权重分布，新版本概率更高)
    android_versions = [
        ("10", 0.3),  # Android 10
        ("11", 0.35), # Android 11
        ("12", 0.25), # Android 12
        ("13", 0.1),  # Android 13
    ]

    # 根据权重选择Android版本
    android_version = random.choices(
        [v[0] for v in android_versions],
        weights=[v[1] for v in android_versions]
    )[0]

    # 生成随机的设备标识符
    def generate_random_id(length, chars=string.ascii_lowercase + string.digits):
        return ''.join(random.choices(chars, k=length))

    # 生成设备指纹
    fingerprint = {
        "brand": device["brand"],
        "model": device["model"],
        "device_name": device["name"],
        "android_version": android_version,
        "build_id": generate_random_id(8).upper(),  # 如: QP1A.190711.020
        "serial": generate_random_id(16),  # 设备序列号
        "android_id": generate_random_id(16),  # Android ID
        "imei": generate_imei(),  # IMEI
        "mac_address": generate_mac_address(),  # MAC地址
        "screen_width": random.choice([1080, 1440, 2160]),
        "screen_height": random.choice([1920, 2560, 3840]),
        "density": random.choice([2.0, 2.75, 3.0, 3.5]),
    }

    return fingerprint

def generate_imei():
    """生成随机但格式正确的IMEI"""
    # IMEI前14位
    imei_start = ''.join(random.choices(string.digits, k=14))

    # 计算校验位 (Luhn算法)
    def luhn_checksum(card_num):
        def digits_of(n):
            return [int(d) for d in str(n)]
        digits = digits_of(card_num)
        odd_digits = digits[-1::-2]
        even_digits = digits[-2::-2]
        checksum = sum(odd_digits)
        for d in even_digits:
            checksum += sum(digits_of(d*2))
        return checksum % 10

    check_digit = (10 - luhn_checksum(int(imei_start))) % 10
    return imei_start + str(check_digit)

def generate_mac_address():
    """生成随机MAC地址"""
    mac = [random.randint(0x00, 0xff) for _ in range(6)]
    # 确保第一个字节的最低位为0 (单播地址)
    mac[0] = mac[0] & 0xfe
    return ':'.join(f'{x:02x}' for x in mac)

def generate_user_agent(fingerprint):
    """根据设备指纹生成User-Agent"""
    android_version = fingerprint["android_version"]
    model = fingerprint["model"]
    build_id = fingerprint["build_id"]

    # 生成WebKit版本
    webkit_version = f"537.36"
    chrome_version = random.choice([
        "91.0.4472.120",
        "92.0.4515.159",
        "93.0.4577.82",
        "94.0.4606.85",
        "95.0.4638.74"
    ])

    user_agent = (
        f"Mozilla/5.0 (Linux; Android {android_version}; {model}) "
        f"AppleWebKit/{webkit_version} (KHTML, like Gecko) "
        f"Chrome/{chrome_version} Mobile Safari/{webkit_version}"
    )

    return user_agent

def get_captcha_image(session):
    """获取验证码图片"""
    try:
        print("正在获取验证码...")
        response = session.get(CAPTCHA_URL, headers={
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Connection': 'keep-alive',
            'Host': 'huanqiu.cgi913.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        })

        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1:
                code_id = data['data']['code_id']
                captcha_base64 = data['data']['captcha_src']

                # 解析base64图片
                if captcha_base64.startswith('data:image/png;base64,'):
                    captcha_base64 = captcha_base64.replace('data:image/png;base64,', '')

                captcha_image = base64.b64decode(captcha_base64)
                return code_id, captcha_image
            else:
                print(f"获取验证码失败: {data.get('msg', '未知错误')}")
                return None, None
        else:
            print(f"请求验证码失败，状态码: {response.status_code}")
            return None, None

    except Exception as e:
        print(f"获取验证码时发生错误: {e}")
        return None, None

def recognize_captcha(captcha_image, max_retries=3):
    """
    智能验证码识别 - 优先使用ddddocr自动识别，失败时提供手动输入
    """
    if OCR_AVAILABLE:
        print("🤖 使用ddddocr自动识别验证码...")

        for attempt in range(max_retries):
            try:
                # 创建OCR实例
                ocr = ddddocr.DdddOcr(show_ad=False)

                # 识别验证码
                result = ocr.classification(captcha_image)

                # 验证结果格式 (通常验证码是4位数字)
                if result and len(result) >= 3 and result.isdigit():
                    print(f"✅ 自动识别成功: {result}")
                    return result
                else:
                    print(f"⚠️ 识别结果可疑: {result} (尝试 {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        continue

            except Exception as e:
                print(f"❌ 自动识别失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    continue

        print("🔄 自动识别多次失败，切换到手动输入模式...")
    else:
        print("📝 ddddocr未安装，使用手动输入模式...")

    # 手动输入模式
    try:
        # 保存验证码图片供用户查看
        captcha_filename = f"captcha_{int(time.time())}.png"
        with open(captcha_filename, "wb") as f:
            f.write(captcha_image)
        print(f"💾 验证码已保存为: {captcha_filename}")

        # 尝试自动显示图片
        try:
            img = Image.open(BytesIO(captcha_image))
            img.show()
            print("🖼️ 验证码图片已自动打开")
        except Exception as e:
            print(f"⚠️ 无法自动显示图片: {e}")
            print(f"📂 请手动打开文件: {captcha_filename}")

        # 获取用户输入
        while True:
            captcha_code = input("\n🔤 请输入验证码 (输入 'retry' 重新获取): ").strip()

            if captcha_code.lower() == 'retry':
                return 'retry'
            elif captcha_code and len(captcha_code) >= 3:
                print(f"✅ 手动输入验证码: {captcha_code}")
                return captcha_code
            else:
                print("❌ 验证码格式不正确，请重新输入")

    except Exception as e:
        print(f"💥 处理验证码时发生错误: {e}")
        return None

# --- 主要注册函数 ---

def auto_register():
    """自动注册账号"""
    try:
        print("=" * 50)
        print("🚀 环球网站自动注册脚本启动")
        print("=" * 50)

        # 生成随机账号信息
        username = generate_random_username()
        password = generate_random_password()

        print(f"📱 生成的账号信息:")
        print(f"   用户名: {username}")
        print(f"   密码: {password}")

        # 创建会话
        session = requests.Session()

        # 获取验证码
        print("\n🔍 正在获取验证码...")
        code_id, captcha_image = get_captcha_image(session)

        if not code_id or not captcha_image:
            print("❌ 获取验证码失败")
            return False

        # 识别验证码
        print("🤖 正在识别验证码...")
        captcha_code = recognize_captcha(captcha_image)

        if not captcha_code:
            print("❌ 验证码识别失败")
            return False

        print(f"✅ 验证码识别成功: {captcha_code}")

        # 获取邀请码
        invitation = input("\n请输入邀请码 (默认: 267524): ").strip()
        if not invitation:
            invitation = "267524"

        paypassword = input("请输入支付密码 (默认: 147258): ").strip()
        if not paypassword:
            paypassword = "147258"

        # 生成sign参数
        print("\n🔐 正在生成加密签名...")
        sign_value = generate_sign_parameter(username, password)

        if not sign_value:
            print("❌ 生成签名失败")
            return False

        # 构造注册请求数据
        register_data = {
            'sign': sign_value,
            'code_id': code_id,
            'code': captcha_code,
            'invitation': invitation,
            'paypassword': paypassword
        }

        print(f"\n📤 准备发送注册请求...")
        print(f"   code_id: {code_id}")
        print(f"   验证码: {captcha_code}")
        print(f"   邀请码: {invitation}")
        print(f"   支付密码: {paypassword}")

        # 发送注册请求
        headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://huanqiu.cgi913.com',
            'Referer': 'https://huanqiu.cgi913.com/',
            'Connection': 'keep-alive',
            'Host': 'huanqiu.cgi913.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        }

        response = session.post(REGISTER_URL, data=register_data, headers=headers)

        print(f"\n📥 服务器响应:")
        print(f"   状态码: {response.status_code}")

        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

                if result.get('code') == 1:
                    print("\n🎉 注册成功!")

                    # 保存账号信息
                    with open(OUTPUT_FILE, "a", encoding="utf-8") as f:
                        f.write(f"{username}:{password}\n")

                    print(f"✅ 账号信息已保存到: {OUTPUT_FILE}")
                    return True
                else:
                    print(f"\n❌ 注册失败: {result.get('msg', '未知错误')}")
                    return False

            except json.JSONDecodeError:
                print(f"   响应内容 (非JSON): {response.text}")
                return False
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False

    except Exception as e:
        print(f"\n💥 注册过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

# --- 主程序入口 ---

if __name__ == "__main__":
    try:
        print("🌟 环球网站自动注册脚本")
        print("🔧 基于JavaScript逆向分析实现")
        print("=" * 50)

        while True:
            success = auto_register()

            if success:
                print("\n✅ 本次注册完成!")
            else:
                print("\n❌ 本次注册失败!")

            user_input = input("\n是否继续注册下一个账号? (Y/N，直接回车=Y): ").strip().upper()
            if user_input == 'N':
                break
            print("\n" + "=" * 50)

    except KeyboardInterrupt:
        print("\n\n👋 程序已退出。")
    except Exception as e:
        print(f"\n💥 程序运行时发生错误: {e}")
        import traceback
        traceback.print_exc()