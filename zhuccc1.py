#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 环球网站 - 自动注册脚本 (带验证码识别)

import requests
import json
import base64
import random
import string
import time
import re
import hashlib
import hmac
from urllib.parse import urlencode
from io import BytesIO
from PIL import Image
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes

# --- 配置项 ---
BASE_URL = "https://huanqiu.cgi913.com"
CAPTCHA_URL = f"{BASE_URL}/api/common/getCaptchaImg"
REGISTER_URL = f"{BASE_URL}/api/login/register"
UPLOAD_URL = f"{BASE_URL}/api/common/upload"
REAL_INFO_URL = f"{BASE_URL}/api/user/updateRealInfo"
USER_INFO_URL = f"{BASE_URL}/api/user/userInfo"
OUTPUT_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\xianbao_data\zhanghao.txt" # 请确保路径存在

# 身份证图片目录
ID_CARD_DIR = r"C:\Users\<USER>\Desktop\hook\zhuce\id_card_sorted"
ID_CARD_FRONT_DIR = f"{ID_CARD_DIR}\\正面"
ID_CARD_BACK_DIR = f"{ID_CARD_DIR}\\反面"

# 加密配置 (从JavaScript逆向得到)
AES_KEY = "v4NTEx37"  # 从JavaScript代码中找到的密钥
AES_KEY_BYTES = AES_KEY.encode('utf-8')

# 验证码识别相关
try:
    import ddddocr
    OCR_AVAILABLE = True
    print("✓ ddddocr 验证码识别库已加载")
except ImportError:
    OCR_AVAILABLE = False
    print("⚠ ddddocr 未安装，将使用手动输入验证码模式")

# OCR身份证识别相关
try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
    print("✓ OpenCV 图像处理库已加载")
except ImportError:
    CV2_AVAILABLE = False
    print("⚠ OpenCV 未安装，身份证识别功能受限")

# 加密相关
import hashlib
import hmac
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes

# --- 核心加密函数 (基于JavaScript逆向) ---

def generate_sign_parameter(username, password):
    """
    生成sign参数 - 完全模拟JavaScript的加密逻辑
    对应JavaScript中的: yv.AES.encrypt(JSON.stringify({username, password}), "v4NTEx37", {format: Av}).toString()
    """
    try:
        # 1. 构造要加密的数据
        data_to_encrypt = {
            "username": username,
            "password": password
        }

        # 2. 转换为JSON字符串 (对应JavaScript的JSON.stringify)
        json_str = json.dumps(data_to_encrypt, separators=(',', ':'), ensure_ascii=False)
        print(f"要加密的JSON: {json_str}")

        # 3. 生成随机IV和Salt (模拟CryptoJS的行为)
        iv = get_random_bytes(16)  # 16字节的随机IV
        salt = get_random_bytes(8)  # 8字节的随机Salt

        # 4. 使用PBKDF2从密钥和salt生成实际的加密密钥 (模拟CryptoJS的密钥派生)
        from Crypto.Protocol.KDF import PBKDF2
        from Crypto.Hash import SHA1

        # CryptoJS默认使用PBKDF2进行密钥派生
        derived_key = PBKDF2(AES_KEY, salt, 32, count=1000, hmac_hash_module=SHA1)

        # 5. AES加密
        cipher = AES.new(derived_key, AES.MODE_CBC, iv)
        padded_data = pad(json_str.encode('utf-8'), AES.block_size)
        ciphertext = cipher.encrypt(padded_data)

        # 6. 构造结果对象 (对应JavaScript的Av.stringify函数)
        result = {
            "ct": base64.b64encode(ciphertext).decode('utf-8'),  # ciphertext转base64
            "iv": iv.hex(),  # IV转hex字符串
            "s": salt.hex()   # salt转hex字符串
        }

        # 7. 转换为JSON字符串 (对应JavaScript的JSON.stringify(t))
        sign_value = json.dumps(result, separators=(',', ':'))
        print(f"生成的sign参数: {sign_value}")

        return sign_value

    except Exception as e:
        print(f"生成sign参数时发生错误: {e}")
        return None

# --- 辅助函数 ---

def generate_random_phone():
    """生成随机手机号"""
    prefix = random.choice(['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                           '150', '151', '152', '153', '155', '156', '157', '158', '159',
                           '180', '181', '182', '183', '184', '185', '186', '187', '188', '189'])
    suffix = ''.join(random.choices(string.digits, k=8))
    return prefix + suffix

def generate_random_password():
    """生成8-12位小写英文和数字混合的随机密码"""
    length = random.randint(8, 12)  # 随机长度8-12位
    # 小写字母和数字
    chars = string.ascii_lowercase + string.digits

    # 确保至少包含一个字母和一个数字
    password = []
    password.append(random.choice(string.ascii_lowercase))  # 至少一个字母
    password.append(random.choice(string.digits))  # 至少一个数字

    # 填充剩余位数
    for _ in range(length - 2):
        password.append(random.choice(chars))

    # 打乱顺序
    random.shuffle(password)
    return ''.join(password)

def generate_real_phone_number():
    """生成真实格式的手机号"""
    # 中国大陆手机号段 (更真实的号段)
    prefixes = [
        # 中国移动
        '134', '135', '136', '137', '138', '139', '147', '150', '151',
        '152', '157', '158', '159', '178', '182', '183', '184', '187', '188',
        # 中国联通
        '130', '131', '132', '145', '155', '156', '166', '175', '176', '185', '186',
        # 中国电信
        '133', '149', '153', '173', '177', '180', '181', '189', '199'
    ]

    prefix = random.choice(prefixes)
    # 后8位数字
    suffix = ''.join(random.choices(string.digits, k=8))
    return prefix + suffix

def generate_device_fingerprint():
    """
    根据抓包数据格式生成真实的设备指纹
    基于实际iPhone设备的User-Agent格式
    """

    # 基于抓包数据的真实iPhone设备信息
    iphone_devices = [
        {
            "model": "iPhone13,2",  # iPhone 12
            "ios_version": "16_6",
            "device_name": "iPhone 12",
            "webkit_version": "605.1.15"
        },
        {
            "model": "iPhone14,3",  # iPhone 13 Pro
            "ios_version": "16_6",
            "device_name": "iPhone 13 Pro",
            "webkit_version": "605.1.15"
        },
        {
            "model": "iPhone15,2",  # iPhone 14
            "ios_version": "16_6",
            "device_name": "iPhone 14",
            "webkit_version": "605.1.15"
        },
        {
            "model": "iPhone12,1",  # iPhone 11
            "ios_version": "16_6",
            "device_name": "iPhone 11",
            "webkit_version": "605.1.15"
        },
        {
            "model": "iPhone11,8",  # iPhone XR
            "ios_version": "16_6",
            "device_name": "iPhone XR",
            "webkit_version": "605.1.15"
        }
    ]

    # 随机选择设备
    device = random.choice(iphone_devices)

    # 生成设备指纹信息
    fingerprint = {
        "model": device["model"],
        "ios_version": device["ios_version"],
        "device_name": device["device_name"],
        "webkit_version": device["webkit_version"],
        "safari_version": "604.1",

        # 生成唯一设备标识
        "device_id": generate_device_id(),
        "session_id": generate_session_id(),

        # 网络相关
        "connection_type": random.choice(["wifi", "cellular"]),
        "carrier": random.choice(["中国移动", "中国联通", "中国电信"]),

        # 屏幕信息 (iPhone真实分辨率)
        "screen_info": get_iphone_screen_info(device["model"]),

        # 时区和语言
        "timezone": "Asia/Shanghai",
        "language": "zh-CN",
        "locale": "zh_CN"
    }

    return fingerprint

def get_iphone_screen_info(model):
    """根据iPhone型号返回对应的屏幕信息"""
    screen_configs = {
        "iPhone13,2": {"width": 390, "height": 844, "scale": 3.0},  # iPhone 12
        "iPhone14,3": {"width": 393, "height": 852, "scale": 3.0},  # iPhone 13 Pro
        "iPhone15,2": {"width": 393, "height": 852, "scale": 3.0},  # iPhone 14
        "iPhone12,1": {"width": 414, "height": 896, "scale": 2.0},  # iPhone 11
        "iPhone11,8": {"width": 414, "height": 896, "scale": 2.0},  # iPhone XR
    }
    return screen_configs.get(model, {"width": 390, "height": 844, "scale": 3.0})

def generate_device_id():
    """生成设备ID (模拟iOS设备标识符格式)"""
    # iOS设备标识符格式: XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
    parts = [
        ''.join(random.choices(string.ascii_uppercase + string.digits, k=8)),
        ''.join(random.choices(string.ascii_uppercase + string.digits, k=4)),
        ''.join(random.choices(string.ascii_uppercase + string.digits, k=4)),
        ''.join(random.choices(string.ascii_uppercase + string.digits, k=4)),
        ''.join(random.choices(string.ascii_uppercase + string.digits, k=12))
    ]
    return '-'.join(parts)

def generate_session_id():
    """生成会话ID"""
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))

# --- 身份信息生成函数 ---

def generate_chinese_name():
    """生成随机中文姓名"""
    # 常见姓氏
    surnames = [
        '王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴',
        '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗',
        '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧',
        '程', '曹', '袁', '邓', '许', '傅', '沈', '曾', '彭', '吕'
    ]

    # 常见名字用字
    given_names = [
        '伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋',
        '勇', '艳', '杰', '娟', '涛', '明', '超', '秀', '霞', '平',
        '刚', '桂', '英', '华', '玉', '萍', '红', '娥', '玲', '菊',
        '辉', '婷', '云', '帆', '凯', '悦', '思', '晨', '宇', '欣'
    ]

    surname = random.choice(surnames)

    # 70%概率生成两字名，30%概率生成单字名
    if random.random() < 0.7:
        given_name = random.choice(given_names) + random.choice(given_names)
    else:
        given_name = random.choice(given_names)

    return surname + given_name

def generate_id_card_number():
    """生成身份证号码（18位）"""
    # 地区代码（前6位）- 使用真实的地区代码
    area_codes = [
        '110101',  # 北京市东城区
        '110102',  # 北京市西城区
        '120101',  # 天津市和平区
        '130102',  # 石家庄市长安区
        '140105',  # 太原市小店区
        '150102',  # 呼和浩特市新城区
        '210102',  # 沈阳市和平区
        '220102',  # 长春市南关区
        '230103',  # 哈尔滨市南岗区
        '310101',  # 上海市黄浦区
        '320102',  # 南京市玄武区
        '330102',  # 杭州市上城区
        '340102',  # 合肥市瑶海区
        '350102',  # 福州市鼓楼区
        '360102',  # 南昌市东湖区
        '370102',  # 济南市历下区
        '410102',  # 郑州市中原区
        '420102',  # 武汉市江岸区
        '430102',  # 长沙市芙蓉区
        '440103',  # 广州市荔湾区
        '450102',  # 南宁市兴宁区
        '460105',  # 海口市秀英区
        '500101',  # 重庆市万州区
        '510104',  # 成都市锦江区
        '520102',  # 贵阳市南明区
        '530102',  # 昆明市五华区
        '610102',  # 西安市新城区
        '620102',  # 兰州市城关区
        '630102',  # 西宁市城东区
        '640104',  # 银川市兴庆区
        '650102',  # 乌鲁木齐市天山区
    ]

    area_code = random.choice(area_codes)

    # 出生日期（8位）- 生成1980-2000年的日期
    year = random.randint(1980, 2000)
    month = random.randint(1, 12)
    day = random.randint(1, 28)  # 使用28避免月份天数问题
    birth_date = f"{year:04d}{month:02d}{day:02d}"

    # 顺序码（3位）- 随机生成
    sequence = f"{random.randint(1, 999):03d}"

    # 前17位
    id_17 = area_code + birth_date + sequence

    # 计算校验码（第18位）
    weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

    sum_val = sum(int(id_17[i]) * weights[i] for i in range(17))
    check_code = check_codes[sum_val % 11]

    return id_17 + check_code

def generate_real_address():
    """生成真实的中国地址"""
    provinces = [
        "北京市", "天津市", "河北省", "山西省", "内蒙古自治区",
        "辽宁省", "吉林省", "黑龙江省", "上海市", "江苏省",
        "浙江省", "安徽省", "福建省", "江西省", "山东省",
        "河南省", "湖北省", "湖南省", "广东省", "广西壮族自治区",
        "海南省", "重庆市", "四川省", "贵州省", "云南省",
        "西藏自治区", "陕西省", "甘肃省", "青海省", "宁夏回族自治区",
        "新疆维吾尔自治区"
    ]

    cities = {
        "北京市": ["东城区", "西城区", "朝阳区", "丰台区", "石景山区", "海淀区"],
        "上海市": ["黄浦区", "徐汇区", "长宁区", "静安区", "普陀区", "虹口区"],
        "广东省": ["广州市", "深圳市", "珠海市", "汕头市", "佛山市", "韶关市"],
        "江苏省": ["南京市", "无锡市", "徐州市", "常州市", "苏州市", "南通市"],
        "浙江省": ["杭州市", "宁波市", "温州市", "嘉兴市", "湖州市", "绍兴市"],
        "山东省": ["济南市", "青岛市", "淄博市", "枣庄市", "东营市", "烟台市"],
        "河南省": ["郑州市", "开封市", "洛阳市", "平顶山市", "安阳市", "鹤壁市"],
        "四川省": ["成都市", "自贡市", "攀枝花市", "泸州市", "德阳市", "绵阳市"],
        "湖北省": ["武汉市", "黄石市", "十堰市", "宜昌市", "襄阳市", "鄂州市"],
        "湖南省": ["长沙市", "株洲市", "湘潭市", "衡阳市", "邵阳市", "岳阳市"]
    }

    streets = [
        "人民路", "解放路", "建设路", "中山路", "和平路", "友谊路",
        "胜利路", "光明路", "幸福路", "团结路", "民主路", "自由路",
        "繁荣路", "发展路", "振兴路", "复兴路", "新华路", "文化路",
        "教育路", "科技路", "工业路", "商业路", "农业路", "环城路"
    ]

    # 选择省份
    province = random.choice(list(cities.keys()))
    city = random.choice(cities[province])
    street = random.choice(streets)
    number = random.randint(1, 999)

    return f"{province}{city}{street}{number}号"

def generate_user_agent(fingerprint):
    """
    根据设备指纹生成User-Agent - 完全模拟抓包数据格式
    抓包数据: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
    """
    model = fingerprint["model"]
    ios_version = fingerprint["ios_version"]
    webkit_version = fingerprint["webkit_version"]
    safari_version = fingerprint["safari_version"]

    # 生成随机的Build号 (iOS格式: 15E148)
    build_number = generate_ios_build_number()

    # 构造完全符合抓包格式的User-Agent
    user_agent = (
        f"Mozilla/5.0 (iPhone; CPU iPhone OS {ios_version} like Mac OS X) "
        f"AppleWebKit/{webkit_version} (KHTML, like Gecko) "
        f"Version/{ios_version.replace('_', '.')} Mobile/{build_number} Safari/{safari_version}"
    )

    return user_agent

def generate_ios_build_number():
    """生成iOS Build号格式 (如: 15E148)"""
    # iOS Build号格式: [主版本号][字母][数字]
    major_version = random.choice(['15', '16', '17'])
    letter = random.choice(['A', 'B', 'C', 'D', 'E', 'F', 'G'])
    number = random.randint(100, 999)
    return f"{major_version}{letter}{number}"

def generate_request_headers(fingerprint, extra_headers=None):
    """
    生成完整的请求头 - 基于抓包数据
    """
    user_agent = generate_user_agent(fingerprint)

    # 基础请求头 (完全模拟抓包数据)
    headers = {
        'User-Agent': user_agent,
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Connection': 'keep-alive',
        'Host': 'huanqiu.cgi913.com',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Token': '',  # 从抓包数据中看到的Token字段
    }

    # 添加额外的请求头
    if extra_headers:
        headers.update(extra_headers)

    return headers

def generate_device_fingerprint_for_request():
    """
    为请求生成设备指纹参数
    可能需要在请求中包含的设备相关参数
    """
    fingerprint = generate_device_fingerprint()

    # 可能在请求中需要的设备参数
    device_params = {
        'device_id': fingerprint['device_id'],
        'device_type': 'ios',
        'device_model': fingerprint['model'],
        'os_version': fingerprint['ios_version'].replace('_', '.'),
        'app_version': '1.0.0',  # 假设的应用版本
        'screen_width': fingerprint['screen_info']['width'],
        'screen_height': fingerprint['screen_info']['height'],
        'timezone': fingerprint['timezone'],
        'language': fingerprint['language'],
    }

    return fingerprint, device_params

def get_captcha_image(session):
    """获取验证码图片"""
    try:
        print("正在获取验证码...")
        response = session.get(CAPTCHA_URL, headers={
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Connection': 'keep-alive',
            'Host': 'huanqiu.cgi913.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        })

        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1:
                code_id = data['data']['code_id']
                captcha_base64 = data['data']['captcha_src']

                # 解析base64图片
                if captcha_base64.startswith('data:image/png;base64,'):
                    captcha_base64 = captcha_base64.replace('data:image/png;base64,', '')

                captcha_image = base64.b64decode(captcha_base64)
                return code_id, captcha_image
            else:
                print(f"获取验证码失败: {data.get('msg', '未知错误')}")
                return None, None
        else:
            print(f"请求验证码失败，状态码: {response.status_code}")
            return None, None

    except Exception as e:
        print(f"获取验证码时发生错误: {e}")
        return None, None

def recognize_captcha(captcha_image, max_retries=3):
    """
    智能验证码识别 - 优先使用ddddocr自动识别，失败时提供手动输入
    """
    if OCR_AVAILABLE:
        print("🤖 使用ddddocr自动识别验证码...")

        for attempt in range(max_retries):
            try:
                # 创建OCR实例
                ocr = ddddocr.DdddOcr(show_ad=False)

                # 识别验证码
                result = ocr.classification(captcha_image)

                # 验证结果格式 (通常验证码是4位数字)
                if result and len(result) >= 3 and result.isdigit():
                    print(f"✅ 自动识别成功: {result}")
                    return result
                else:
                    print(f"⚠️ 识别结果可疑: {result} (尝试 {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        continue

            except Exception as e:
                print(f"❌ 自动识别失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    continue

        print("🔄 自动识别多次失败，切换到手动输入模式...")
    else:
        print("📝 ddddocr未安装，使用手动输入模式...")

    # 手动输入模式
    try:
        # 保存验证码图片供用户查看
        captcha_filename = f"captcha_{int(time.time())}.png"
        with open(captcha_filename, "wb") as f:
            f.write(captcha_image)
        print(f"💾 验证码已保存为: {captcha_filename}")

        # 尝试自动显示图片
        try:
            img = Image.open(BytesIO(captcha_image))
            img.show()
            print("🖼️ 验证码图片已自动打开")
        except Exception as e:
            print(f"⚠️ 无法自动显示图片: {e}")
            print(f"📂 请手动打开文件: {captcha_filename}")

        # 获取用户输入
        while True:
            captcha_code = input("\n🔤 请输入验证码 (输入 'retry' 重新获取): ").strip()

            if captcha_code.lower() == 'retry':
                return 'retry'
            elif captcha_code and len(captcha_code) >= 3:
                print(f"✅ 手动输入验证码: {captcha_code}")
                return captcha_code
            else:
                print("❌ 验证码格式不正确，请重新输入")

    except Exception as e:
        print(f"💥 处理验证码时发生错误: {e}")
        return None

# --- 身份证OCR识别函数 ---

def extract_id_card_info(image_path):
    """
    从身份证正面图片中提取姓名和身份证号码
    """
    try:
        print(f"🔍 正在识别身份证信息: {image_path}")

        if not OCR_AVAILABLE:
            print("❌ ddddocr未安装，无法进行OCR识别")
            return None, None

        # 读取图片
        with open(image_path, 'rb') as f:
            image_bytes = f.read()

        # 使用ddddocr进行OCR识别
        ocr = ddddocr.DdddOcr(show_ad=False)

        # 如果有OpenCV，先进行图像预处理
        if CV2_AVAILABLE:
            try:
                # 使用OpenCV进行图像预处理
                import cv2
                import numpy as np

                # 将字节转换为numpy数组
                nparr = np.frombuffer(image_bytes, np.uint8)
                img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

                # 转换为灰度图
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

                # 增强对比度
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
                enhanced = clahe.apply(gray)

                # 转换回字节
                _, buffer = cv2.imencode('.jpg', enhanced)
                image_bytes = buffer.tobytes()

                print("✅ 图像预处理完成")
            except Exception as e:
                print(f"⚠️ 图像预处理失败，使用原图: {e}")

        # OCR识别
        result = ocr.classification(image_bytes)
        print(f"📝 OCR识别结果: {result}")

        # 解析识别结果
        name, id_number = parse_id_card_text(result)

        if name and id_number:
            print(f"✅ 身份证信息提取成功:")
            print(f"   姓名: {name}")
            print(f"   身份证号: {id_number}")
            return name, id_number
        else:
            print("❌ 无法从OCR结果中提取有效信息")
            return None, None

    except Exception as e:
        print(f"❌ 身份证OCR识别失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def parse_id_card_text(ocr_text):
    """
    从OCR识别的文本中解析出姓名和身份证号码
    """
    try:
        import re

        if not ocr_text:
            return None, None

        print(f"🔍 解析OCR文本: {ocr_text}")

        # 身份证号码正则表达式 (18位)
        id_pattern = r'[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[0-9Xx]'
        id_matches = re.findall(id_pattern, ocr_text)

        id_number = None
        if id_matches:
            # 找到完整的身份证号码
            for match in re.finditer(id_pattern, ocr_text):
                id_number = match.group()
                break

        # 姓名提取 - 身份证上的姓名通常在特定位置
        name = None

        # 方法1: 查找"姓名"关键字后的内容
        name_patterns = [
            r'姓名[：:\s]*([^\s\d]{2,4})',
            r'姓\s*名[：:\s]*([^\s\d]{2,4})',
            r'名[：:\s]*([^\s\d]{2,4})',
        ]

        for pattern in name_patterns:
            matches = re.findall(pattern, ocr_text)
            if matches:
                name = matches[0].strip()
                break

        # 方法2: 如果没找到，尝试查找中文姓名模式
        if not name:
            # 查找2-4个连续中文字符，且不是常见的身份证字段
            chinese_pattern = r'[\u4e00-\u9fa5]{2,4}'
            chinese_matches = re.findall(chinese_pattern, ocr_text)

            # 过滤掉常见的身份证字段
            exclude_words = ['中华人民共和国', '居民身份证', '公民身份', '姓名', '性别', '民族',
                           '出生', '住址', '身份证', '有效期', '签发机关', '年月日']

            for match in chinese_matches:
                if len(match) >= 2 and len(match) <= 4:
                    if not any(word in match for word in exclude_words):
                        # 检查是否像姓名（常见姓氏开头）
                        common_surnames = ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴',
                                         '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗']
                        if match[0] in common_surnames:
                            name = match
                            break

        # 方法3: 手动输入模式
        if not name or not id_number:
            print("⚠️ 自动识别不完整，请手动确认:")

            if not name:
                manual_name = input(f"请输入姓名 (OCR识别: {ocr_text[:50]}...): ").strip()
                if manual_name:
                    name = manual_name

            if not id_number:
                manual_id = input("请输入身份证号码: ").strip()
                if manual_id and len(manual_id) == 18:
                    id_number = manual_id

        return name, id_number

    except Exception as e:
        print(f"❌ 解析OCR文本失败: {e}")
        return None, None

# --- 图片上传和实名认证函数 ---

def get_random_id_card_images():
    """随机获取身份证正反面图片"""
    import os
    import glob

    try:
        # 获取正面图片
        front_pattern = os.path.join(ID_CARD_FRONT_DIR, "*.*")
        front_images = glob.glob(front_pattern)

        # 获取反面图片
        back_pattern = os.path.join(ID_CARD_BACK_DIR, "*.*")
        back_images = glob.glob(back_pattern)

        if not front_images or not back_images:
            print(f"❌ 身份证图片目录为空:")
            print(f"   正面目录: {ID_CARD_FRONT_DIR}")
            print(f"   反面目录: {ID_CARD_BACK_DIR}")
            return None, None

        front_image = random.choice(front_images)
        back_image = random.choice(back_images)

        print(f"📷 选择的身份证图片:")
        print(f"   正面: {os.path.basename(front_image)}")
        print(f"   反面: {os.path.basename(back_image)}")

        return front_image, back_image

    except Exception as e:
        print(f"❌ 获取身份证图片失败: {e}")
        return None, None

def upload_image(session, image_path, token):
    """上传单张图片"""
    try:
        import os

        if not os.path.exists(image_path):
            print(f"❌ 图片文件不存在: {image_path}")
            return None

        filename = os.path.basename(image_path)

        # 构造multipart/form-data请求
        files = {
            'file': (filename, open(image_path, 'rb'), 'image/jpeg')
        }

        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Origin': 'https://huanqiu.cgi913.com',
            'Referer': 'https://huanqiu.cgi913.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Token': token,
            'User-Agent': session.headers.get('User-Agent', '')
        }

        print(f"📤 上传图片: {filename}")
        response = session.post(UPLOAD_URL, files=files, headers=headers)

        # 关闭文件
        files['file'][1].close()

        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('code') == 1:
                    url = result['data']['url']
                    print(f"✅ 图片上传成功: {url}")
                    return url
                else:
                    print(f"❌ 图片上传失败: {result.get('msg', '未知错误')}")
                    return None
            except json.JSONDecodeError:
                print(f"❌ 上传响应解析失败: {response.text}")
                return None
        else:
            print(f"❌ 图片上传请求失败，状态码: {response.status_code}")
            return None

    except Exception as e:
        print(f"❌ 图片上传过程中发生错误: {e}")
        return None

def get_user_info(session, token):
    """获取用户信息"""
    try:
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Token': token,
            'User-Agent': session.headers.get('User-Agent', '')
        }

        response = session.get(USER_INFO_URL, headers=headers)

        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('code') == 1:
                    return result.get('data', {})
                else:
                    print(f"❌ 获取用户信息失败: {result.get('msg', '未知错误')}")
                    return None
            except json.JSONDecodeError:
                print(f"❌ 用户信息响应解析失败: {response.text}")
                return None
        else:
            print(f"❌ 获取用户信息请求失败，状态码: {response.status_code}")
            return None

    except Exception as e:
        print(f"❌ 获取用户信息过程中发生错误: {e}")
        return None

def submit_real_info(session, token, phone_number, name, id_card, address, front_url, back_url):
    """提交实名认证信息"""
    try:
        # 构造实名认证数据
        real_info_data = {
            'nickname': name,
            'sf_type': '1',  # 身份证类型
            'id_card': id_card,
            'sfaddress': address,
            'phone': phone_number,
            'id_card_image': f"{front_url},{back_url}"  # 正面,反面
        }

        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://huanqiu.cgi913.com',
            'Referer': 'https://huanqiu.cgi913.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Token': token,
            'User-Agent': session.headers.get('User-Agent', '')
        }

        print(f"📋 提交实名认证信息:")
        print(f"   姓名: {name}")
        print(f"   身份证: {id_card}")
        print(f"   地址: {address}")
        print(f"   手机号: {phone_number}")

        response = session.post(REAL_INFO_URL, data=real_info_data, headers=headers)

        if response.status_code == 200:
            try:
                result = response.json()
                print(f"📥 实名认证响应: {json.dumps(result, ensure_ascii=False)}")

                if result.get('code') == 1:
                    print("✅ 实名认证提交成功!")
                    return True, None
                else:
                    error_msg = result.get('msg', '未知错误')
                    print(f"❌ 实名认证提交失败: {error_msg}")
                    return False, error_msg
            except json.JSONDecodeError:
                print(f"❌ 实名认证响应解析失败: {response.text}")
                return False, "响应解析失败"
        else:
            print(f"❌ 实名认证请求失败，状态码: {response.status_code}")
            return False, f"请求失败: {response.status_code}"

    except Exception as e:
        print(f"❌ 实名认证过程中发生错误: {e}")
        return False, str(e)

def auto_real_name_verification_with_retry(session, token, phone_number, max_retries=3):
    """自动实名认证流程 - 支持重试机制"""
    try:
        print("\n" + "=" * 50)
        print("🆔 开始自动实名认证流程 (支持重试)")
        print("=" * 50)

        used_id_cards = set()  # 记录已使用的身份证号，避免重复

        for attempt in range(max_retries):
            print(f"\n🔄 实名认证尝试 {attempt + 1}/{max_retries}")

            # 1. 获取身份证图片
            front_image, back_image = get_random_id_card_images()
            if not front_image or not back_image:
                print("❌ 无法获取身份证图片，跳过实名认证")
                return False

            # 2. OCR识别身份证正面信息
            print(f"🔍 识别身份证信息...")
            name, id_card = extract_id_card_info(front_image)

            if not name or not id_card:
                print("❌ 身份证信息识别失败")
                if attempt < max_retries - 1:
                    print("🔄 尝试使用新的身份证图片...")
                    continue
                else:
                    print("❌ 多次识别失败，跳过实名认证")
                    return False

            # 检查是否已使用过这个身份证号
            if id_card in used_id_cards:
                print(f"⚠️ 身份证号 {id_card} 已在本次尝试中使用过，重新选择...")
                continue

            used_id_cards.add(id_card)

            # 3. 生成地址信息
            address = generate_real_address()

            print(f"\n👤 使用的身份信息:")
            print(f"   姓名: {name} (OCR识别)")
            print(f"   身份证: {id_card} (OCR识别)")
            print(f"   地址: {address} (随机生成)")

            # 4. 上传正面图片
            print(f"\n📤 上传身份证正面...")
            front_url = upload_image(session, front_image, token)
            if not front_url:
                print("❌ 正面图片上传失败")
                if attempt < max_retries - 1:
                    print("🔄 尝试重新上传...")
                    continue
                else:
                    return False

            # 5. 上传反面图片
            print(f"📤 上传身份证反面...")
            back_url = upload_image(session, back_image, token)
            if not back_url:
                print("❌ 反面图片上传失败")
                if attempt < max_retries - 1:
                    print("🔄 尝试重新上传...")
                    continue
                else:
                    return False

            # 6. 提交实名认证信息
            print(f"\n📋 提交实名认证...")
            success, error_msg = submit_real_info(session, token, phone_number, name, id_card, address, front_url, back_url)

            if success:
                print("🎉 实名认证完成!")
                print(f"✅ 认证信息: {name} ({id_card})")

                # 获取用户信息确认实名状态
                print(f"\n🔍 确认实名认证状态...")
                user_info = get_user_info(session, token)
                if user_info:
                    sfz_status = user_info.get('sfz_status', 0)
                    isshiming = user_info.get('isshiming', 0)
                    print(f"📋 实名状态: sfz_status={sfz_status}, isshiming={isshiming}")

                return True
            else:
                print(f"❌ 实名认证失败: {error_msg}")

                # 检查是否是"已绑定"错误，需要重试
                if error_msg and ("已绑定" in error_msg or "已存在" in error_msg or "重复" in error_msg or "已经绑定" in error_msg):
                    print("🔄 检测到身份证已被绑定，尝试使用新的身份证...")
                    if attempt < max_retries - 1:
                        print(f"⏳ 等待3秒后重试...")
                        time.sleep(3)
                        continue
                    else:
                        print("❌ 多次重试仍失败，可能所有身份证都已被使用")
                        return False
                else:
                    # 其他错误，不重试
                    print("❌ 实名认证失败，错误不可重试")
                    return False

        print("❌ 实名认证重试次数用尽")
        return False

    except Exception as e:
        print(f"❌ 实名认证流程发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def auto_real_name_verification(session, token, phone_number):
    """自动实名认证流程 - 使用OCR识别真实身份信息"""
    try:
        print("\n" + "=" * 50)
        print("🆔 开始自动实名认证流程")
        print("=" * 50)

        # 1. 获取身份证图片
        front_image, back_image = get_random_id_card_images()
        if not front_image or not back_image:
            print("❌ 无法获取身份证图片，跳过实名认证")
            return False

        # 2. OCR识别身份证正面信息
        print(f"\n� 识别身份证信息...")
        name, id_card = extract_id_card_info(front_image)

        if not name or not id_card:
            print("❌ 身份证信息识别失败，跳过实名认证")
            return False

        # 3. 生成地址信息
        address = generate_real_address()

        print(f"\n👤 使用的身份信息:")
        print(f"   姓名: {name} (OCR识别)")
        print(f"   身份证: {id_card} (OCR识别)")
        print(f"   地址: {address} (随机生成)")

        # 4. 上传正面图片
        print(f"\n📤 上传身份证正面...")
        front_url = upload_image(session, front_image, token)
        if not front_url:
            print("❌ 正面图片上传失败")
            return False

        # 5. 上传反面图片
        print(f"📤 上传身份证反面...")
        back_url = upload_image(session, back_image, token)
        if not back_url:
            print("❌ 反面图片上传失败")
            return False

        # 6. 提交实名认证信息
        print(f"\n📋 提交实名认证...")
        success, error_msg = submit_real_info(session, token, phone_number, name, id_card, address, front_url, back_url)

        if success:
            print("🎉 实名认证完成!")
            print(f"✅ 认证信息: {name} ({id_card})")

            # 获取用户信息确认实名状态
            print(f"\n🔍 确认实名认证状态...")
            user_info = get_user_info(session, token)
            if user_info:
                sfz_status = user_info.get('sfz_status', 0)
                isshiming = user_info.get('isshiming', 0)
                sfnumber = user_info.get('sfnumber', '')
                nickname = user_info.get('nickname', '')

                print(f"📋 实名认证状态:")
                print(f"   身份证状态 (sfz_status): {sfz_status}")
                print(f"   实名状态 (isshiming): {isshiming}")
                print(f"   认证姓名: {nickname}")
                print(f"   身份证号: {sfnumber}")

                if sfz_status == 1:
                    print("✅ 实名认证完全成功!")
                    return True
                else:
                    print("⚠️ 实名认证状态异常，但提交成功")
                    return True
            else:
                print("⚠️ 无法获取用户信息，但实名认证提交成功")
                return True
        else:
            print(f"❌ 实名认证失败: {error_msg}")

            # 检查是否是"已绑定"错误
            if error_msg and ("已绑定" in error_msg or "已存在" in error_msg or "重复" in error_msg):
                print("🔄 检测到身份证已被绑定，建议重新运行脚本使用新的身份证")

            return False

    except Exception as e:
        print(f"❌ 实名认证流程发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

# --- 登录函数 ---

def auto_login(phone_number, password, session=None):
    """自动登录验证账号"""
    try:
        print(f"\n🔑 正在验证登录: {phone_number}")

        if session is None:
            session = requests.Session()
            # 生成设备指纹和请求头
            fingerprint, _ = generate_device_fingerprint_for_request()
            headers = generate_request_headers(fingerprint)
            session.headers.update(headers)

        # 生成登录的sign参数 (只包含手机号和密码)
        login_sign = generate_sign_parameter(phone_number, password)

        if not login_sign:
            print("❌ 生成登录签名失败")
            return False, None

        # 构造登录请求数据
        login_data = {
            'sign': login_sign
        }

        # 发送登录请求
        login_url = f"{BASE_URL}/api/login/login"
        response = session.post(login_url, data=login_data)

        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('code') == 1:
                    userinfo = result.get('data', {}).get('userinfo', {})
                    token = userinfo.get('token', '')
                    user_id = userinfo.get('user_id', '')

                    print(f"✅ 登录成功!")
                    print(f"   用户ID: {user_id}")
                    print(f"   Token: {token[:20]}...")
                    print(f"   过期时间: {userinfo.get('expires_in', 0)}秒")

                    return True, {
                        'token': token,
                        'user_id': user_id,
                        'userinfo': userinfo
                    }
                else:
                    print(f"❌ 登录失败: {result.get('msg', '未知错误')}")
                    return False, None
            except json.JSONDecodeError:
                print(f"❌ 登录响应解析失败: {response.text}")
                return False, None
        else:
            print(f"❌ 登录请求失败，状态码: {response.status_code}")
            return False, None

    except Exception as e:
        print(f"💥 登录过程中发生错误: {e}")
        return False, None

# --- 主要注册函数 ---

def auto_register():
    """自动注册账号 - 集成设备指纹伪装"""
    try:
        print("=" * 60)
        print("🚀 环球网站自动注册脚本启动")
        print("=" * 60)

        # 1. 生成设备指纹
        print("📱 正在生成设备指纹...")
        fingerprint, device_params = generate_device_fingerprint_for_request()

        print(f"🔧 设备信息:")
        print(f"   设备: {fingerprint['device_name']}")
        print(f"   型号: {fingerprint['model']}")
        print(f"   iOS版本: {fingerprint['ios_version'].replace('_', '.')}")
        print(f"   屏幕分辨率: {fingerprint['screen_info']['width']}x{fingerprint['screen_info']['height']}")
        print(f"   设备ID: {fingerprint['device_id'][:20]}...")
        print(f"   语言: {fingerprint['language']}")
        print(f"   时区: {fingerprint['timezone']}")

        # 2. 生成随机账号信息 (使用真实手机号)
        phone_number = generate_real_phone_number()
        password = generate_random_password()

        print(f"\n� 生成的账号信息:")
        print(f"   手机号: {phone_number}")
        print(f"   密码: {password} (长度: {len(password)}位)")

        # 3. 创建会话并设置请求头 (完全模拟抓包数据)
        session = requests.Session()

        # 生成完整的请求头
        base_headers = generate_request_headers(fingerprint)
        session.headers.update(base_headers)

        print(f"\n🌐 User-Agent: {base_headers['User-Agent'][:80]}...")

        # 4. 获取验证码 (支持重试)
        max_captcha_retries = 3
        for captcha_attempt in range(max_captcha_retries):
            print(f"\n🔍 正在获取验证码... (尝试 {captcha_attempt + 1}/{max_captcha_retries})")
            code_id, captcha_image = get_captcha_image(session)

            if not code_id or not captcha_image:
                print("❌ 获取验证码失败")
                if captcha_attempt < max_captcha_retries - 1:
                    print("🔄 等待3秒后重试...")
                    time.sleep(3)
                    continue
                else:
                    return False

            # 5. 识别验证码
            print("🤖 正在识别验证码...")
            captcha_code = recognize_captcha(captcha_image)

            if captcha_code == 'retry':
                print("🔄 用户选择重新获取验证码...")
                continue
            elif not captcha_code:
                print("❌ 验证码识别失败")
                if captcha_attempt < max_captcha_retries - 1:
                    continue
                else:
                    return False

            print(f"✅ 验证码识别成功: {captcha_code}")
            break
        else:
            print("❌ 验证码获取/识别失败次数过多")
            return False

        # 6. 获取邀请码和支付密码
        invitation = input("\n🎫 请输入邀请码 (默认: 267524): ").strip()
        if not invitation:
            invitation = "267524"

        paypassword = input("🔐 请输入支付密码 (默认: 147258): ").strip()
        if not paypassword:
            paypassword = "147258"

        # 7. 生成sign参数
        print("\n🔐 正在生成加密签名...")
        sign_value = generate_sign_parameter(phone_number, password)

        if not sign_value:
            print("❌ 生成签名失败")
            return False

        # 8. 构造注册请求数据
        register_data = {
            'sign': sign_value,
            'code_id': code_id,
            'code': captcha_code,
            'invitation': invitation,
            'paypassword': paypassword
        }

        print(f"\n📤 准备发送注册请求...")
        print(f"   设备: {fingerprint['device_name']}")
        print(f"   code_id: {code_id}")
        print(f"   验证码: {captcha_code}")
        print(f"   邀请码: {invitation}")
        print(f"   支付密码: {paypassword}")

        # 9. 发送注册请求
        register_headers = base_headers.copy()
        register_headers.update({
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://huanqiu.cgi913.com',
            'Referer': 'https://huanqiu.cgi913.com/',
        })

        print("\n🚀 发送注册请求...")
        response = session.post(REGISTER_URL, data=register_data, headers=register_headers)

        print(f"\n📥 服务器响应:")
        print(f"   状态码: {response.status_code}")

        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

                if result.get('code') == 1:
                    print("\n🎉 注册成功!")

                    # 获取注册返回的用户信息
                    userinfo = result.get('data', {}).get('userinfo', {})
                    user_id = userinfo.get('user_id', '')
                    token = userinfo.get('token', '')

                    print(f"📋 注册信息:")
                    print(f"   用户ID: {user_id}")
                    print(f"   Token: {token[:20]}...")
                    print(f"   昵称: {userinfo.get('nickname', '')}")

                    # 验证登录功能
                    print("\n🔍 验证登录功能...")
                    login_success, login_info = auto_login(phone_number, password, session)

                    if login_success:
                        print("✅ 登录验证通过!")

                        # 询问是否进行实名认证
                        do_real_name = input("\n🆔 是否进行自动实名认证? (Y/N，默认Y): ").strip().upper()
                        if do_real_name != 'N':
                            print("\n🚀 开始实名认证流程...")
                            real_name_success = auto_real_name_verification(session, token, phone_number)

                            if real_name_success:
                                print("🎉 完整流程成功!")
                                print("✅ 注册 → 登录 → 实名认证 全部完成!")

                                # 再次获取用户信息显示最终状态
                                final_user_info = get_user_info(session, token)
                                if final_user_info:
                                    final_nickname = final_user_info.get('nickname', '')
                                    final_sfnumber = final_user_info.get('sfnumber', '')
                                    print(f"🆔 最终认证信息: {final_nickname} ({final_sfnumber})")

                                account_status = "注册+登录+实名认证成功"
                            else:
                                print("⚠️ 实名认证失败，但账号注册成功")
                                account_status = "注册+登录成功,实名认证失败"
                        else:
                            account_status = "注册+登录成功,跳过实名认证"

                        # 保存完整的账号信息
                        account_info = (
                            f"{phone_number}:{password}:{fingerprint['device_name']}:"
                            f"{user_id}:{token[:20]}...:{account_status}\n"
                        )
                        with open(OUTPUT_FILE, "a", encoding="utf-8") as f:
                            f.write(account_info)

                        print(f"✅ 账号信息已保存到: {OUTPUT_FILE}")
                        print(f"📱 设备信息: {fingerprint['device_name']} ({fingerprint['model']})")
                        return True
                    else:
                        print("⚠️ 注册成功但登录验证失败")
                        # 仍然保存账号信息
                        account_info = f"{phone_number}:{password}:{fingerprint['device_name']}:登录验证失败\n"
                        with open(OUTPUT_FILE, "a", encoding="utf-8") as f:
                            f.write(account_info)
                        return True
                else:
                    print(f"\n❌ 注册失败: {result.get('msg', '未知错误')}")
                    return False

            except json.JSONDecodeError:
                print(f"   响应内容 (非JSON): {response.text}")
                return False
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False

    except Exception as e:
        print(f"\n💥 注册过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

# --- 主程序入口 ---

if __name__ == "__main__":
    try:
        print("🌟 环球网站自动注册脚本")
        print("🔧 基于JavaScript逆向分析实现")
        print("=" * 50)

        while True:
            success = auto_register()

            if success:
                print("\n✅ 本次注册完成!")
            else:
                print("\n❌ 本次注册失败!")

            user_input = input("\n是否继续注册下一个账号? (Y/N，直接回车=Y): ").strip().upper()
            if user_input == 'N':
                break
            print("\n" + "=" * 50)

    except KeyboardInterrupt:
        print("\n\n👋 程序已退出。")
    except Exception as e:
        print(f"\n💥 程序运行时发生错误: {e}")
        import traceback
        traceback.print_exc()