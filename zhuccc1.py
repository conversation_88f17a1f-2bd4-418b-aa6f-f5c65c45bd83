#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 环球网站 - 自动注册脚本 (带验证码识别)

import requests
import json
import base64
import random
import string
import time
import re
import hashlib
import hmac
from urllib.parse import urlencode
from io import BytesIO
from PIL import Image
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes

# --- 配置项 ---
BASE_URL = "https://huanqiu.cgi913.com"
CAPTCHA_URL = f"{BASE_URL}/api/common/getCaptchaImg"
REGISTER_URL = f"{BASE_URL}/api/login/register"
OUTPUT_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\xianbao_data\zhanghao.txt" # 请确保路径存在

# 加密配置 (从JavaScript逆向得到)
AES_KEY = "v4NTEx37"  # 从JavaScript代码中找到的密钥
AES_KEY_BYTES = AES_KEY.encode('utf-8')

# 验证码识别相关
try:
    import ddddocr
    OCR_AVAILABLE = True
    print("✓ ddddocr 验证码识别库已加载")
except ImportError:
    OCR_AVAILABLE = False
    print("⚠ ddddocr 未安装，将使用手动输入验证码模式")

# 加密相关
import hashlib
import hmac
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes

# --- 核心加密函数 (基于JavaScript逆向) ---

def generate_sign_parameter(username, password):
    """
    生成sign参数 - 完全模拟JavaScript的加密逻辑
    对应JavaScript中的: yv.AES.encrypt(JSON.stringify({username, password}), "v4NTEx37", {format: Av}).toString()
    """
    try:
        # 1. 构造要加密的数据
        data_to_encrypt = {
            "username": username,
            "password": password
        }

        # 2. 转换为JSON字符串 (对应JavaScript的JSON.stringify)
        json_str = json.dumps(data_to_encrypt, separators=(',', ':'), ensure_ascii=False)
        print(f"要加密的JSON: {json_str}")

        # 3. 生成随机IV和Salt (模拟CryptoJS的行为)
        iv = get_random_bytes(16)  # 16字节的随机IV
        salt = get_random_bytes(8)  # 8字节的随机Salt

        # 4. 使用PBKDF2从密钥和salt生成实际的加密密钥 (模拟CryptoJS的密钥派生)
        from Crypto.Protocol.KDF import PBKDF2
        from Crypto.Hash import SHA1

        # CryptoJS默认使用PBKDF2进行密钥派生
        derived_key = PBKDF2(AES_KEY, salt, 32, count=1000, hmac_hash_module=SHA1)

        # 5. AES加密
        cipher = AES.new(derived_key, AES.MODE_CBC, iv)
        padded_data = pad(json_str.encode('utf-8'), AES.block_size)
        ciphertext = cipher.encrypt(padded_data)

        # 6. 构造结果对象 (对应JavaScript的Av.stringify函数)
        result = {
            "ct": base64.b64encode(ciphertext).decode('utf-8'),  # ciphertext转base64
            "iv": iv.hex(),  # IV转hex字符串
            "s": salt.hex()   # salt转hex字符串
        }

        # 7. 转换为JSON字符串 (对应JavaScript的JSON.stringify(t))
        sign_value = json.dumps(result, separators=(',', ':'))
        print(f"生成的sign参数: {sign_value}")

        return sign_value

    except Exception as e:
        print(f"生成sign参数时发生错误: {e}")
        return None

# --- 辅助函数 ---

def generate_random_phone():
    """生成随机手机号"""
    prefix = random.choice(['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                           '150', '151', '152', '153', '155', '156', '157', '158', '159',
                           '180', '181', '182', '183', '184', '185', '186', '187', '188', '189'])
    suffix = ''.join(random.choices(string.digits, k=8))
    return prefix + suffix

def generate_random_password(length=8):
    """生成随机密码，只包含数字"""
    return ''.join(random.choices(string.digits, k=length))

def generate_random_username():
    """生成随机用户名"""
    prefixes = ["user", "test", "demo", "temp", "guest"]
    suffix = ''.join(random.choices(string.digits, k=6))
    return random.choice(prefixes) + suffix

def get_captcha_image(session):
    """获取验证码图片"""
    try:
        print("正在获取验证码...")
        response = session.get(CAPTCHA_URL, headers={
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Connection': 'keep-alive',
            'Host': 'huanqiu.cgi913.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        })

        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1:
                code_id = data['data']['code_id']
                captcha_base64 = data['data']['captcha_src']

                # 解析base64图片
                if captcha_base64.startswith('data:image/png;base64,'):
                    captcha_base64 = captcha_base64.replace('data:image/png;base64,', '')

                captcha_image = base64.b64decode(captcha_base64)
                return code_id, captcha_image
            else:
                print(f"获取验证码失败: {data.get('msg', '未知错误')}")
                return None, None
        else:
            print(f"请求验证码失败，状态码: {response.status_code}")
            return None, None

    except Exception as e:
        print(f"获取验证码时发生错误: {e}")
        return None, None

def recognize_captcha(captcha_image):
    """识别验证码"""
    if OCR_AVAILABLE:
        try:
            ocr = ddddocr.DdddOcr()
            result = ocr.classification(captcha_image)
            print(f"自动识别验证码: {result}")
            return result
        except Exception as e:
            print(f"自动识别验证码失败: {e}")

    # 手动输入模式
    try:
        # 保存验证码图片供用户查看
        with open("captcha_temp.png", "wb") as f:
            f.write(captcha_image)
        print("验证码已保存为 captcha_temp.png，请查看后手动输入")

        # 尝试显示图片
        try:
            img = Image.open(BytesIO(captcha_image))
            img.show()
        except:
            pass

        captcha_code = input("请输入验证码: ").strip()
        return captcha_code
    except Exception as e:
        print(f"处理验证码时发生错误: {e}")
        return None

# --- 主要注册函数 ---

def auto_register():
    """自动注册账号"""
    try:
        print("=" * 50)
        print("🚀 环球网站自动注册脚本启动")
        print("=" * 50)

        # 生成随机账号信息
        username = generate_random_username()
        password = generate_random_password()

        print(f"📱 生成的账号信息:")
        print(f"   用户名: {username}")
        print(f"   密码: {password}")

        # 创建会话
        session = requests.Session()

        # 获取验证码
        print("\n🔍 正在获取验证码...")
        code_id, captcha_image = get_captcha_image(session)

        if not code_id or not captcha_image:
            print("❌ 获取验证码失败")
            return False

        # 识别验证码
        print("🤖 正在识别验证码...")
        captcha_code = recognize_captcha(captcha_image)

        if not captcha_code:
            print("❌ 验证码识别失败")
            return False

        print(f"✅ 验证码识别成功: {captcha_code}")

        # 获取邀请码
        invitation = input("\n请输入邀请码 (默认: 267524): ").strip()
        if not invitation:
            invitation = "267524"

        paypassword = input("请输入支付密码 (默认: 147258): ").strip()
        if not paypassword:
            paypassword = "147258"

        # 生成sign参数
        print("\n🔐 正在生成加密签名...")
        sign_value = generate_sign_parameter(username, password)

        if not sign_value:
            print("❌ 生成签名失败")
            return False

        # 构造注册请求数据
        register_data = {
            'sign': sign_value,
            'code_id': code_id,
            'code': captcha_code,
            'invitation': invitation,
            'paypassword': paypassword
        }

        print(f"\n📤 准备发送注册请求...")
        print(f"   code_id: {code_id}")
        print(f"   验证码: {captcha_code}")
        print(f"   邀请码: {invitation}")
        print(f"   支付密码: {paypassword}")

        # 发送注册请求
        headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://huanqiu.cgi913.com',
            'Referer': 'https://huanqiu.cgi913.com/',
            'Connection': 'keep-alive',
            'Host': 'huanqiu.cgi913.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        }

        response = session.post(REGISTER_URL, data=register_data, headers=headers)

        print(f"\n📥 服务器响应:")
        print(f"   状态码: {response.status_code}")

        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

                if result.get('code') == 1:
                    print("\n🎉 注册成功!")

                    # 保存账号信息
                    with open(OUTPUT_FILE, "a", encoding="utf-8") as f:
                        f.write(f"{username}:{password}\n")

                    print(f"✅ 账号信息已保存到: {OUTPUT_FILE}")
                    return True
                else:
                    print(f"\n❌ 注册失败: {result.get('msg', '未知错误')}")
                    return False

            except json.JSONDecodeError:
                print(f"   响应内容 (非JSON): {response.text}")
                return False
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False

    except Exception as e:
        print(f"\n💥 注册过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

# --- [新功能] 手机号验证预请求函数 ---
def verify_telephone_pre_request(session, phone, area_code, language, headers):
    print("\n--- [步骤1] 正在发送手机号验证预请求 ---")
    salt = str(int(time.time() * 1000))
    secret = base64.b64encode(random.getrandbits(128).to_bytes(16, 'big')).decode('utf-8')
    params = {'areaCode': area_code, 'telephone': phone, 'language': language, 'salt': salt, 'secret': secret}
    try:
        response = session.get(VERIFY_URL, params=params, headers=headers)
        response.raise_for_status()
        response_json = response.json()
        print("服务器响应:", json.dumps(response_json, ensure_ascii=False))
        if response_json.get("resultCode") == 1:
            print(">>> 手机号预验证成功，服务器已授权后续注册。")
            return True
        else:
            print(f">>> 手机号预验证失败: {response_json.get('resultMsg', '未知错误')}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"预请求网络错误: {e}")
        return False

# --- 主函数 (整合了所有新功能) ---
def auto_register():
    try:
        # [功能1] 交互式输入邀请码
        invite_code = input("请输入您的邀请码并按回车: ").strip()
        if not invite_code:
            print("错误：邀请码不能为空。程序已退出。")
            return

        # 固定的元数据
        area_code = "92"
        sex = "1"
        birthday = "1753510077"
        language = "zh"
        
        # [功能2] 动态生成设备指纹
        print("\n--- [动态] 正在生成本次注册信息 ---")
        fingerprint = generate_dynamic_fingerprint()
        device_model = fingerprint['model']
        device_os_version = fingerprint['os_version']
        device_serial = fingerprint['serial']

        # 生成其他随机信息
        phone = generate_random_phone()
        password = generate_random_password()
        nickname = f"{generate_random_chinese_name()}-黄朝凤团队"
        
        print(f"  手机号: {phone}\n  密码: {password}\n  昵称: {nickname}")
        print(f"  设备型号: {device_model}\n  安卓版本: {device_os_version}\n  设备序列号: {device_serial}")
        print(f"  邀请码: {invite_code} (由您输入)")

        session = requests.Session()
        headers = {
            "User-Agent": f"chat_im/2.1.8 (Linux; U; Android {device_os_version}; {device_model} Build/UP1A.231005.007)",
            "Connection": "Keep-Alive",
            "Accept-Encoding": "gzip"
        }
    
        # [功能3] 执行预请求
        if not verify_telephone_pre_request(session, phone, area_code, language, headers):
            print("\n!!! 流程终止，无法进行下一步注册。 !!!")
            return

        print("\n--- [步骤2] 准备正式注册请求 ---")
        salt = str(int(time.time() * 1000))
        encrypted_password = encrypt_login_password(password)
        mac_content_to_sign = f"212i919292901{area_code}{language}{phone}{sex}{salt}"
        mac_signature = generate_mac_signature(GLOBAL_AES_KEY, mac_content_to_sign)

        payload = {
            "birthday": birthday, "smsCode": "", "sex": sex, "telephone": phone,
            "cityId": "0", "provinceId": "0", "countryId": "0", "mac": mac_signature,
            "password": encrypted_password, "areaCode": area_code, "areaId": "0",
            "apiVersion": "76", "osVersion": device_os_version, "serial": device_serial,
            "inviteCode": invite_code, "idcard": "", "nickname": nickname,
            "isSmsRegister": "0", "name": "", "xmppVersion": "1",
            "model": device_model, "userType": "0"
        }
        
        encrypted_data = encrypt_request_data(payload)
        random_secret = base64.b64encode(random.getrandbits(128).to_bytes(16, 'big')).decode('utf-8')
        params = {"data": encrypted_data, "deviceId": "android", "language": language, "salt": salt, "secret": random_secret}

        print("--- 正在发送正式注册请求 ---")
        response = session.get(REGISTER_URL, params=params, headers=headers)
        response.raise_for_status()
        response_json = response.json()

        print("\n--- 服务器原始响应 ---")
        print(json.dumps(response_json, indent=2, ensure_ascii=False))

        if "data" in response_json and isinstance(response_json.get("data"), dict) and "data" in response_json["data"]:
            print("\n--- 尝试解密响应内容 ---")
            decrypted_resp = decrypt_response_data(response_json["data"]["data"])
            print(">>> 响应解密成功! <<<")
            print(json.dumps(decrypted_resp, indent=2, ensure_ascii=False))
            if response_json.get("resultCode") == 1:
                print("\n\n>>> 逻辑判断：\033[92m注册成功!\033[0m <<<") # 使用绿色高亮
                with open(OUTPUT_FILE, "a", encoding="utf-8") as f:
                    f.write(f"{phone}:{password}:{nickname}\n")
                print(f"\n账号信息已保存到: {OUTPUT_FILE}")
            else:
                print(f"\n\n>>> 逻辑判断：\033[91m注册失败 (ResultCode: {response_json.get('resultCode')}) - {response_json.get('resultMsg', '')}\033[0m <<<")
        else:
            error_msg = response_json.get("resultMsg", "未知错误")
            print(f"\n\n>>> 逻辑判断：\033[91m注册失败 ({error_msg})\033[0m <<<")

    except requests.exceptions.RequestException as e:
        print(f"\n网络请求错误: {e}")
    except KeyboardInterrupt:
        print("\n\n操作已由用户手动中断。程序退出。")
    except Exception as e:
        print(f"\n发生未知错误: {e}")

if __name__ == "__main__":
    try:
        while True:
            auto_register()
            user_input = input("\n是否继续注册下一个账号? (Y/N，直接回车=Y): ").strip().upper()
            if user_input == 'N':
                break
            print("-" * 50)
    except KeyboardInterrupt:
        print("\n\n程序已退出。")