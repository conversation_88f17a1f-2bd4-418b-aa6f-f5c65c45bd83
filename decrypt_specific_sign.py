#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 解密特定sign数据

import json
import base64
from urllib.parse import unquote
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import hashlib
import hmac

def decrypt_sign_data():
    """解密你提供的sign数据"""
    print("🔍 解密sign数据")
    print("=" * 50)
    
    # 你提供的完整数据
    raw_data = "sign=%7B%22ct%22%3A%22d8%2B1K3VFqOWPoZxFeaY4GePDAkLSXRsLIxmO%2FJOwRUgNcmwpHmUCxqtSpUrppoGm%22%2C%22iv%22%3A%22fc959df3b5cbe47782e6c267450b5795%22%2C%22s%22%3A%228802485aa551eada%22%7D&code_id=17538686036889e93b57e57973100&code=9753&invitation=263154&paypassword=147258"
    
    print(f"📝 原始数据:")
    print(f"   {raw_data}")
    
    # URL解码
    decoded_data = unquote(raw_data)
    print(f"\n📝 URL解码后:")
    print(f"   {decoded_data}")
    
    # 提取sign参数
    sign_part = decoded_data.split('&')[0].replace('sign=', '')
    print(f"\n📝 Sign部分:")
    print(f"   {sign_part}")
    
    # 解析JSON
    try:
        sign_json = json.loads(sign_part)
        print(f"\n📝 Sign JSON解析:")
        print(f"   ct (密文): {sign_json['ct']}")
        print(f"   iv (初始向量): {sign_json['iv']}")
        print(f"   s (盐值): {sign_json['s']}")
        
        ct = sign_json['ct']
        iv = sign_json['iv']
        s = sign_json['s']
        
        # 尝试不同的密钥
        print(f"\n🔐 尝试解密...")
        
        # 可能的密钥列表
        possible_keys = [
            "1234567890123456",  # 16字节标准密钥
            "abcdef1234567890",
            "huanqiu12345678",
            "cgi91312345678ab",
            "0123456789abcdef",
            "fedcba0987654321",
            s + "12345678",      # 使用盐值构造密钥
            s[:16].ljust(16, '0'),  # 盐值填充到16位
        ]
        
        # 尝试基于盐值生成密钥
        salt_based_keys = []
        try:
            # 方法1: 直接使用盐值
            if len(s) >= 16:
                salt_based_keys.append(s[:16])
            else:
                salt_based_keys.append(s.ljust(16, '0'))
            
            # 方法2: MD5哈希盐值
            salt_md5 = hashlib.md5(s.encode()).hexdigest()[:16]
            salt_based_keys.append(salt_md5)
            
            # 方法3: SHA256哈希盐值
            salt_sha256 = hashlib.sha256(s.encode()).hexdigest()[:16]
            salt_based_keys.append(salt_sha256)
            
        except Exception as e:
            print(f"⚠️ 生成基于盐值的密钥失败: {e}")
        
        # 合并所有可能的密钥
        all_keys = possible_keys + salt_based_keys
        
        decrypted_success = False
        for i, key in enumerate(all_keys):
            try:
                print(f"   尝试密钥 {i+1}: {key}")
                
                key_bytes = key.encode('utf-8')[:16]  # 确保16字节
                if len(key_bytes) < 16:
                    key_bytes = key_bytes.ljust(16, b'0')
                
                iv_bytes = bytes.fromhex(iv)
                ct_bytes = base64.b64decode(ct)
                
                cipher = AES.new(key_bytes, AES.MODE_CBC, iv_bytes)
                decrypted = unpad(cipher.decrypt(ct_bytes), AES.block_size)
                decrypted_text = decrypted.decode('utf-8')
                
                print(f"✅ 解密成功!")
                print(f"   使用密钥: {key}")
                print(f"   解密结果: {decrypted_text}")
                
                # 尝试解析为JSON
                try:
                    decrypted_json = json.loads(decrypted_text)
                    print(f"   JSON格式: {json.dumps(decrypted_json, ensure_ascii=False, indent=2)}")
                    
                    # 分析密码
                    if 'password' in decrypted_json:
                        password = decrypted_json['password']
                        print(f"\n🔐 密码分析:")
                        print(f"   密码: {password}")
                        print(f"   长度: {len(password)}")
                        print(f"   包含小写: {any(c.islower() for c in password)}")
                        print(f"   包含大写: {any(c.isupper() for c in password)}")
                        print(f"   包含数字: {any(c.isdigit() for c in password)}")
                        print(f"   只有字母数字: {password.isalnum()}")
                        
                except json.JSONDecodeError:
                    print(f"   (非JSON格式的文本)")
                
                decrypted_success = True
                break
                
            except Exception as e:
                print(f"   ❌ 失败: {e}")
                continue
        
        if not decrypted_success:
            print("❌ 所有密钥都解密失败")
            
            # 尝试分析密钥生成规律
            print(f"\n🔍 分析密钥生成规律:")
            print(f"   盐值: {s}")
            print(f"   盐值长度: {len(s)}")
            print(f"   盐值十六进制: {s.encode().hex()}")
            
            # 尝试其他可能的密钥生成方式
            print(f"\n💡 可能的密钥生成方式:")
            print(f"   1. 固定密钥 + 盐值")
            print(f"   2. 哈希(盐值 + 固定字符串)")
            print(f"   3. 基于时间戳的密钥")
            print(f"   4. 基于用户名/手机号的密钥")
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
    except Exception as e:
        print(f"❌ 解密过程发生错误: {e}")

def analyze_other_params():
    """分析其他参数"""
    print(f"\n📋 其他参数分析:")
    print(f"=" * 50)
    
    params = {
        'code_id': '17538686036889e93b57e57973100',
        'code': '9753',
        'invitation': '263154',
        'paypassword': '147258'
    }
    
    for key, value in params.items():
        print(f"   {key}: {value}")
    
    print(f"\n💡 参数说明:")
    print(f"   code_id: 验证码ID (时间戳相关)")
    print(f"   code: 验证码 (4位数字)")
    print(f"   invitation: 邀请码")
    print(f"   paypassword: 支付密码")

def compare_with_our_format():
    """对比我们的格式"""
    print(f"\n🔍 对比我们的请求格式:")
    print(f"=" * 50)
    
    print(f"📝 我们的格式:")
    print(f"   Content-Type: application/x-www-form-urlencoded")
    print(f"   参数: sign, code_id, code, invitation, paypassword")
    
    print(f"📝 成功的格式:")
    print(f"   Content-Type: application/x-www-form-urlencoded")
    print(f"   参数: sign, code_id, code, invitation, paypassword")
    print(f"   ✅ 格式一致!")
    
    print(f"\n🔐 关键差异可能在于:")
    print(f"   1. 密码的真实内容和格式")
    print(f"   2. 加密密钥的生成方式")
    print(f"   3. 加密算法的具体实现")

def main():
    """主函数"""
    print("🔍 Sign数据解密分析")
    print("🔧 解密成功注册的sign数据")
    print("=" * 60)
    
    # 解密sign数据
    decrypt_sign_data()
    
    # 分析其他参数
    analyze_other_params()
    
    # 对比格式
    compare_with_our_format()
    
    print("\n" + "=" * 60)
    print("📊 总结:")
    print("1. 如果解密成功，可以看到真实的密码格式")
    print("2. 如果解密失败，说明密钥生成方式不同")
    print("3. 请求格式本身是正确的")
    print("4. 问题可能在密码内容或加密实现")

if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n👋 解密被用户中断")
    except Exception as e:
        print(f"\n💥 解密过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
