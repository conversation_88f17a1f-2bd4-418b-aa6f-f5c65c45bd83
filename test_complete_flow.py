#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 环球网站完整流程测试脚本

import sys
import os
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_environment():
    """测试环境配置"""
    print("🔧 环境配置检查")
    print("=" * 30)
    
    # 检查必要的库
    required_libs = [
        ('requests', 'HTTP请求库'),
        ('Crypto', 'AES加密库'),
        ('PIL', '图像处理库'),
        ('ddddocr', 'OCR识别库')
    ]
    
    missing_libs = []
    for lib, desc in required_libs:
        try:
            __import__(lib)
            print(f"✅ {lib} - {desc}")
        except ImportError:
            print(f"❌ {lib} - {desc} (未安装)")
            missing_libs.append(lib)
    
    # 检查可选库
    optional_libs = [
        ('cv2', 'OpenCV图像处理库')
    ]
    
    for lib, desc in optional_libs:
        try:
            __import__(lib)
            print(f"✅ {lib} - {desc}")
        except ImportError:
            print(f"⚠️ {lib} - {desc} (可选，未安装)")
    
    # 检查目录
    print(f"\n📁 目录检查:")
    directories = [
        r"C:\Users\<USER>\Desktop\hook\zhuce\xianbao_data",
        r"C:\Users\<USER>\Desktop\hook\zhuce\id_card_sorted\正面",
        r"C:\Users\<USER>\Desktop\hook\zhuce\id_card_sorted\反面"
    ]
    
    for directory in directories:
        if os.path.exists(directory):
            file_count = len([f for f in os.listdir(directory) if os.path.isfile(os.path.join(directory, f))])
            print(f"✅ {directory} ({file_count} 个文件)")
        else:
            print(f"❌ {directory} (不存在)")
    
    return len(missing_libs) == 0

def test_basic_functions():
    """测试基本功能"""
    print("\n🧪 基本功能测试")
    print("=" * 30)
    
    try:
        from zhuccc1 import (
            generate_real_phone_number,
            generate_random_password,
            generate_device_fingerprint_for_request,
            generate_sign_parameter,
            generate_chinese_name,
            generate_real_address
        )
        
        # 测试手机号生成
        phone = generate_real_phone_number()
        print(f"📱 手机号: {phone}")
        
        # 测试密码生成
        password = generate_random_password()
        print(f"🔐 密码: {password} (长度: {len(password)})")
        
        # 测试设备指纹
        fingerprint, device_params = generate_device_fingerprint_for_request()
        print(f"📱 设备: {fingerprint['device_name']}")
        
        # 测试签名生成
        sign = generate_sign_parameter(phone, password)
        if sign:
            print(f"🔐 签名: 生成成功 ({len(sign)} 字符)")
        else:
            print(f"❌ 签名生成失败")
            return False
        
        # 测试身份信息生成
        name = generate_chinese_name()
        address = generate_real_address()
        print(f"👤 姓名: {name}")
        print(f"🏠 地址: {address}")
        
        print("✅ 基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ocr_function():
    """测试OCR功能"""
    print("\n🔍 OCR功能测试")
    print("=" * 30)
    
    try:
        from zhuccc1 import get_random_id_card_front_image, extract_id_card_info

        # 获取身份证正面图片
        front_image = get_random_id_card_front_image()

        if not front_image:
            print("❌ 无法获取身份证图片")
            return False

        print(f"📷 正面图片: {os.path.basename(front_image)}")

        # 测试OCR识别
        print("🔍 正在进行OCR识别...")
        name, id_card = extract_id_card_info(front_image)

        if name and id_card:
            print(f"✅ OCR识别成功:")
            print(f"   姓名: {name}")
            print(f"   身份证: {id_card}")
            return True
        else:
            print("❌ OCR识别失败")
            return False
            
    except Exception as e:
        print(f"❌ OCR功能测试失败: {e}")
        return False

def test_proxy_function():
    """测试代理功能"""
    print("\n🌐 代理功能测试")
    print("=" * 30)

    try:
        from zhuccc1 import PROXY_CONFIG, ProxyManager

        if not PROXY_CONFIG['enabled']:
            print("📡 代理功能已禁用")
            print("💡 如需启用，请运行: python proxy_config.py")
            return True

        print("🔧 代理配置:")
        print(f"   套餐ID: {PROXY_CONFIG['packid']}")
        print(f"   用户ID: {PROXY_CONFIG['uid']}")
        print(f"   协议: {PROXY_CONFIG['protocol'].upper()}")

        # 测试代理管理器
        proxy_manager = ProxyManager(
            packid=PROXY_CONFIG['packid'],
            uid=PROXY_CONFIG['uid'],
            access_name=PROXY_CONFIG['access_name'],
            access_password=PROXY_CONFIG['access_password'],
            protocol=PROXY_CONFIG['protocol'],
            time_duration=PROXY_CONFIG['time_duration']
        )

        # 获取代理
        proxy_dict = proxy_manager.get_proxy(force_new=True)

        if proxy_dict:
            print("✅ 代理获取成功")

            # 测试代理连接
            import requests
            test_url = "https://httpbin.org/ip"
            response = requests.get(test_url, proxies=proxy_dict, timeout=10)

            if response.status_code == 200:
                result = response.json()
                print(f"✅ 代理连接测试成功")
                print(f"   代理IP: {result.get('origin', '未知')}")
                return True
            else:
                print(f"❌ 代理连接测试失败")
                return False
        else:
            print("❌ 代理获取失败")
            return False

    except Exception as e:
        print(f"❌ 代理功能测试失败: {e}")
        return False

def test_network_connection():
    """测试网络连接"""
    print("\n🌐 网络连接测试")
    print("=" * 30)

    try:
        import requests

        # 测试基本连接
        print("📡 测试网站连接...")
        response = requests.get("https://huanqiu.cgi913.com", timeout=10)

        if response.status_code == 200:
            print("✅ 网站连接正常")
            return True
        else:
            print(f"⚠️ 网站响应异常: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ 网络连接测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 环球网站注册脚本 - 完整流程测试")
    print("🔧 测试所有功能模块的准备情况")
    print("=" * 60)
    
    tests = [
        ("环境配置", test_environment),
        ("基本功能", test_basic_functions),
        ("OCR识别", test_ocr_function),
        ("代理功能", test_proxy_function),
        ("网络连接", test_network_connection)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}测试:")
        result = test_func()
        results.append(result)
        
        if result:
            print(f"✅ {test_name}测试通过")
        else:
            print(f"❌ {test_name}测试失败")
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通过" if results[i] else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    passed_count = sum(results)
    total_count = len(results)
    
    print(f"\n📈 总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("\n🎉 所有测试通过! 系统准备就绪!")
        print("💡 现在可以运行完整的注册流程:")
        print("   python zhuccc1.py")
        
        print("\n📋 完整流程说明:")
        print("   1. 🔐 注册账号 (手机号 + 密码)")
        print("   2. 🔑 登录验证")
        print("   3. 🆔 实名认证 (OCR识别 + 图片上传)")
        print("   4. 💾 保存完整账号信息")
        
    elif passed_count >= 2:
        print("\n⚠️ 部分功能可用，但建议修复失败的测试项")
        print("💡 可以尝试运行基本注册功能:")
        print("   python zhuccc1.py")
        
    else:
        print("\n❌ 多项测试失败，请先解决环境问题")
        print("🔧 建议操作:")
        print("   1. 运行: python install_requirements.py")
        print("   2. 确保身份证图片目录存在且有图片")
        print("   3. 检查网络连接")
    
    return passed_count == total_count

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
